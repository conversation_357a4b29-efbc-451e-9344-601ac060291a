import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import NewUserWelcome from '../components/NewUserWelcome';

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedTasks: 0,
    pendingTasks: 0,
  });
  const [chartData, setChartData] = useState({
    labels: [],
    datasets: [],
  });

  const [projectDetails, setProjectDetails] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 检查用户状态并获取仪表板数据
  useEffect(() => {
    if (status === 'authenticated') {
      const checkUserStatusAndFetchData = async () => {
        try {
          setLoading(true);
          setError('');

          // 首先检查用户状态
          const statusResponse = await fetch('/api/user/status');
          const statusResult = await statusResponse.json();

          if (statusResult.success) {
            if (!statusResult.data.permissions.canAccessDashboard) {
              // 用户状态不允许访问仪表盘，重定向到状态页面
              router.push('/user-status');
              return;
            }
          }

          // 用户状态正常，获取仪表盘数据
          const response = await fetch('/api/dashboard/stats');

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));

            // 如果是权限相关错误，重定向到状态页面
            if (errorData.code === 'USER_NOT_APPROVED' || errorData.code === 'UNAUTHORIZED') {
              router.push('/user-status');
              return;
            }

            throw new Error(errorData.message || '获取仪表盘数据失败');
          }

          const result = await response.json();

          if (result.success && result.data) {
            setStats(result.data.stats);
            setChartData(result.data.chartData);
            setProjectDetails(result.data.projectDetails || []);
          } else {
            throw new Error(result.message || '获取数据失败');
          }
        } catch (error) {
          console.error('获取仪表板数据失败:', error);
          setError(error instanceof Error ? error.message : '获取数据失败，请稍后再试');
        } finally {
          setLoading(false);
        }
      };

      checkUserStatusAndFetchData();
    }
  }, [status, router]);

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            欢迎回来，{session?.user?.name}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            正在加载您的项目和任务概览...
          </p>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  // 检查是否为新用户（没有项目）
  const isNewUser = stats.totalProjects === 0 && projectDetails.length === 0;

  // 如果是新用户，显示欢迎界面
  if (isNewUser) {
    return (
      <div className="max-w-7xl mx-auto space-y-8">
        {/* 页面标题 */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            团队概览
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            今天是 {new Date().toLocaleDateString('zh-CN', {
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            })}
          </p>
        </div>

        {/* 新用户欢迎界面 */}
        <NewUserWelcome
          userName={session?.user?.name || '用户'}
          userRole={session?.user?.role || 'MEMBER'}
        />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-8">
      {/* 页面标题和概览 */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            团队概览
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            欢迎回来，{session?.user?.name} 👋 今天是 {new Date().toLocaleDateString('zh-CN', {
              month: 'long',
              day: 'numeric',
              weekday: 'long'
            })}
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex items-center space-x-3">
          {/* 移除假的上次登录时间显示 */}
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
          <button
            onClick={() => window.location.reload()}
            className="ml-4 text-sm underline hover:no-underline"
          >
            重新加载
          </button>
        </div>
      )}

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Link href="/projects" className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer overflow-hidden">
          <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -mr-10 -mt-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {stats.totalProjects}
                </p>
              </div>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">研究项目总数</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">点击查看所有项目</p>
            </div>
          </div>
        </Link>

        <Link href="/projects?status=active" className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-green-300 dark:hover:border-green-600 cursor-pointer overflow-hidden">
          <div className="absolute top-0 right-0 w-20 h-20 bg-green-500/10 rounded-full -mr-10 -mt-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">
                  {stats.activeProjects}
                </p>
              </div>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">进行中项目</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">正在积极推进的项目</p>
            </div>
          </div>
        </Link>

        <Link href="/tasks?status=completed" className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-purple-300 dark:hover:border-purple-600 cursor-pointer overflow-hidden">
          <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -mr-10 -mt-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                  {stats.completedTasks}
                </p>
              </div>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">已完成任务</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">团队协作成果</p>
            </div>
          </div>
        </Link>

        <Link href="/tasks?status=pending" className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-orange-300 dark:hover:border-orange-600 cursor-pointer overflow-hidden">
          <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -mr-10 -mt-10"></div>
          <div className="relative">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 text-white shadow-lg">
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors">
                  {stats.pendingTasks}
                </p>
              </div>
            </div>
            <div>
              <p className="text-gray-600 dark:text-gray-400 font-medium">待处理任务</p>
              <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">需要关注的任务</p>
            </div>
          </div>
        </Link>
      </div>



      {/* 团队状态和任务分布 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 团队贡献度 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">团队贡献度</h3>
            <Link href="/team" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              查看详情 →
            </Link>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">团队成员</span>
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {stats.totalUsers || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">活跃项目</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {stats.activeProjects || 0}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-500"
                style={{width: `${stats.activeProjects && stats.totalProjects ? Math.round((stats.activeProjects / stats.totalProjects) * 100) : 0}%`}}
              ></div>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              项目贡献度: {stats.activeProjects && stats.totalProjects ? Math.round((stats.activeProjects / stats.totalProjects) * 100) : 0}%
            </p>
          </div>
        </div>

        {/* 任务完成趋势 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">任务统计</h3>
            <Link href="/tasks" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              详细分析 →
            </Link>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">已完成</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {stats.completedTasks || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">进行中</span>
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {stats.inProgressTasks || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">完成率</span>
              <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                {stats.completedTasks && stats.totalTasks ? Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0}%
              </span>
            </div>
          </div>
        </div>

        {/* 文件统计 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">文件管理</h3>
            <Link href="/files" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              管理文件 →
            </Link>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">总文件数</span>
              <span className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
                {stats.totalFiles || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">项目文件</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {stats.projectFiles || 0}
              </span>
            </div>
            <div className="text-center">
              <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded mb-1">
                <div
                  className="h-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded transition-all duration-500"
                  style={{width: `${stats.totalFiles ? Math.min(100, (stats.totalFiles / 100) * 100) : 0}%`}}
                ></div>
              </div>
              <span className="text-xs text-gray-500 dark:text-gray-400">存储使用情况</span>
            </div>
          </div>
        </div>

        {/* 沟通统计 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">团队沟通</h3>
            <Link href="/chats" className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              查看全部 →
            </Link>
          </div>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">活跃群聊</span>
              <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {stats.activeChats || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">总消息数</span>
              <span className="text-lg font-bold text-green-600 dark:text-green-400">
                {stats.totalMessages || 0}
              </span>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs text-gray-600 dark:text-gray-400">项目讨论</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-xs text-gray-600 dark:text-gray-400">团队协作</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 项目进展概览 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:items-start">
        {/* 项目进展概览 */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden h-full flex flex-col">
            <div className="bg-gradient-to-r from-primary-500 to-primary-600 px-6 py-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-bold text-white">项目进展概览</h2>
                <Link href="/projects" className="text-primary-100 hover:text-white text-sm font-medium transition-colors">
                  查看全部 →
                </Link>
              </div>
            </div>

            <div className="p-6 flex-1">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="relative">
                    <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary-200 border-t-primary-600"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-6 h-6 bg-primary-600 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              ) : projectDetails.length === 0 ? (
                <div className="text-center py-16">
                  <div className="mx-auto w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full flex items-center justify-center mb-4">
                    <svg className="w-12 h-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">还没有项目</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-6">创建您的第一个项目来开始协作</p>
                  <Link href="/projects/new" className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    创建项目
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {projectDetails.map((project: any) => {
                    const totalTasks = project.totalTasks;
                    const completedTasks = project.completedTasks;
                    const inProgressTasks = project.inProgressTasks;
                    const pendingTasks = project.pendingTasks;
                    const progressPercentage = Math.round(project.progress * 100);

                    return (
                      <Link
                        key={project.id}
                        href={`/projects/${project.id}`}
                        className="group relative bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-xl p-6 hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-600 hover:border-primary-300 dark:hover:border-primary-600"
                      >
                        {/* 项目标题和进度 */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-gray-900 dark:text-gray-100 text-lg mb-1 truncate group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" title={project.title}>
                              {project.title}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {totalTasks} 个任务
                            </p>
                          </div>
                          <div className="flex-shrink-0 ml-4">
                            <div className="relative w-16 h-16">
                              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                <path
                                  className="text-gray-200 dark:text-gray-600"
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  fill="none"
                                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                />
                                <path
                                  className={`transition-all duration-500 ${
                                    progressPercentage >= 80 ? 'text-green-500' :
                                    progressPercentage >= 60 ? 'text-yellow-500' :
                                    progressPercentage >= 30 ? 'text-orange-500' : 'text-red-500'
                                  }`}
                                  stroke="currentColor"
                                  strokeWidth="3"
                                  strokeLinecap="round"
                                  fill="none"
                                  strokeDasharray={`${progressPercentage}, 100`}
                                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                  {progressPercentage}%
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 任务状态统计 */}
                        <div className="space-y-3">
                          {/* 已完成任务 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                              <span className="text-sm text-gray-600 dark:text-gray-400">已完成</span>
                            </div>
                            <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                              {completedTasks}
                            </span>
                          </div>

                          {/* 进行中任务 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                              <span className="text-sm text-gray-600 dark:text-gray-400">进行中</span>
                            </div>
                            <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                              {inProgressTasks}
                            </span>
                          </div>

                          {/* 待处理任务 */}
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                              <span className="text-sm text-gray-600 dark:text-gray-400">待处理</span>
                            </div>
                            <span className="text-sm font-semibold text-gray-600 dark:text-gray-400">
                              {pendingTasks}
                            </span>
                          </div>
                        </div>

                        {/* 悬浮效果指示器 */}
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <svg className="w-5 h-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                        </div>
                      </Link>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 快捷功能 */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden h-full flex flex-col">
            <div className="bg-gradient-to-r from-primary-500 to-primary-600 px-6 py-4">
              <h3 className="text-lg font-bold text-white">快捷功能</h3>
            </div>
            <div className="p-6 space-y-3 flex-1 flex flex-col justify-center">
              <Link href="/projects/new" className="group flex items-center w-full p-3 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 text-blue-700 dark:text-blue-300 rounded-lg hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-800/30 dark:hover:to-blue-700/30 transition-all duration-200">
                <div className="p-2 bg-blue-500 text-white rounded-lg mr-3">
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <span className="font-medium">创建项目</span>
              </Link>

              <Link href="/tasks/new" className="group flex items-center w-full p-3 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 text-green-700 dark:text-green-300 rounded-lg hover:from-green-100 hover:to-green-200 dark:hover:from-green-800/30 dark:hover:to-green-700/30 transition-all duration-200">
                <div className="p-2 bg-green-500 text-white rounded-lg mr-3">
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <span className="font-medium">新建任务</span>
              </Link>

              <Link href="/team" className="group flex items-center w-full p-3 bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 text-purple-700 dark:text-purple-300 rounded-lg hover:from-purple-100 hover:to-purple-200 dark:hover:from-purple-800/30 dark:hover:to-purple-700/30 transition-all duration-200">
                <div className="p-2 bg-purple-500 text-white rounded-lg mr-3">
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <span className="font-medium">团队管理</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
