import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import Avatar from './Avatar';
import WeChatStyleFileMessage from './WeChatStyleFileMessage';
import EnhancedEmojiPicker, { EnhancedEmojiRenderer } from './EnhancedEmojiPicker';
import ImageLightbox from './ImageLightbox';
import ChatFilePreview from './ChatFilePreview';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface ChatMessage {
  id: string;
  content: string;
  type: string;
  fileName?: string;
  fileUrl?: string;
  fileSize?: number;
  isSystem: boolean;
  createdAt: string;
  sender?: User;
}

interface Chat {
  id: string;
  type: string;
  name?: string;
  participants: User[];
  otherParticipants: User[];
  messages: ChatMessage[];
}

interface PrivateChatProps {
  chatId?: string;
  recipientId?: string;
  onClose: () => void;
  onChatUpdate?: () => void;
}

export default function PrivateChat({ chatId, recipientId, onClose, onChatUpdate }: PrivateChatProps) {
  const { data: session } = useSession();
  const [chat, setChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [previewFile, setPreviewFile] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [lightboxImages, setLightboxImages] = useState<Array<{src: string; alt?: string; title?: string}>>([]);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [showLightbox, setShowLightbox] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化聊天
  useEffect(() => {
    if (chatId) {
      loadChat(chatId);
    } else if (recipientId) {
      createOrGetChat(recipientId);
    }
  }, [chatId, recipientId]);

  // 标记聊天为已读
  const markChatAsRead = async (id: string) => {
    try {
      // 调用API标记消息为已读
      const response = await fetch(`/api/chats/${id}/read`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // 同时使用localStorage作为备用方案
        const key = `chat_last_view_${id}_${session?.user?.id}`;
        localStorage.setItem(key, new Date().toISOString());

        // 触发聊天列表刷新以更新未读数
        onChatUpdate?.();

        // 触发全局事件，通知MessageCenter刷新未读数
        window.dispatchEvent(new CustomEvent('chatMarkedAsRead'));
      }
    } catch (error) {
      console.error('标记消息已读失败:', error);
    }
  };

  // 加载现有聊天
  const loadChat = async (id: string) => {
    try {
      setLoading(true);

      // 获取聊天信息
      const chatResponse = await fetch('/api/chats');
      const chats = await chatResponse.json();
      const currentChat = chats.find((c: Chat) => c.id === id);

      if (currentChat) {
        setChat(currentChat);
        await loadMessages(id);
      }
    } catch (error) {
      console.error('加载聊天失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 创建或获取聊天
  const createOrGetChat = async (recipientId: string) => {
    try {
      setLoading(true);

      const response = await fetch('/api/chats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          participantIds: [recipientId],
          type: 'PRIVATE',
        }),
      });

      if (response.ok) {
        const newChat = await response.json();
        setChat(newChat);
        await loadMessages(newChat.id);
      }
    } catch (error) {
      console.error('创建聊天失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载消息
  const loadMessages = async (id: string) => {
    try {
      const response = await fetch(`/api/chats/${id}/messages`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
        // 标记聊天为已读
        markChatAsRead(id);
      }
    } catch (error) {
      console.error('加载消息失败:', error);
    }
  };

  // 发送消息
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !chat || sending) return;

    try {
      setSending(true);

      const response = await fetch(`/api/chats/${chat.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          type: 'TEXT',
        }),
      });

      if (response.ok) {
        const message = await response.json();
        setMessages(prev => [...prev, message]);
        setNewMessage('');
        // 触发聊天列表刷新
        onChatUpdate?.();
      }
    } catch (error) {
      console.error('发送消息失败:', error);
    } finally {
      setSending(false);
    }
  };

  // 处理文件上传
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !chat) return;

    try {
      setUploading(true);

      // 上传文件
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch('/api/upload/chat-file', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error('文件上传失败');
      }

      const uploadResult = await uploadResponse.json();

      // 发送文件消息
      const messageResponse = await fetch(`/api/chats/${chat.id}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: uploadResult.fileName,
          type: 'FILE',
          fileName: uploadResult.fileName,
          fileUrl: uploadResult.fileUrl,
          fileSize: uploadResult.fileSize,
        }),
      });

      if (messageResponse.ok) {
        const message = await messageResponse.json();
        setMessages(prev => [...prev, message]);
        // 触发聊天列表刷新
        onChatUpdate?.();
      }
    } catch (error) {
      console.error('文件发送失败:', error);
      alert('文件发送失败，请稍后再试');
    } finally {
      setUploading(false);
      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型
  const getFileType = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      case 'md':
        return 'text/markdown';
      case 'json':
        return 'application/json';
      default:
        return 'application/octet-stream';
    }
  };

  // 添加表情到消息
  const handleEmojiSelect = (emoji: string) => {
    setNewMessage(prev => prev + emoji);
  };

  // 处理图片预览
  const handleImagePreview = (message: ChatMessage) => {
    if (!message.fileUrl || !message.fileName) return;

    // 收集所有图片消息
    const imageMessages = messages.filter(msg =>
      msg.type === 'FILE' &&
      msg.fileName &&
      getFileType(msg.fileName).startsWith('image/')
    );

    const images = imageMessages.map(msg => ({
      src: msg.fileUrl!,
      alt: msg.fileName!,
      title: msg.fileName!
    }));

    const currentIndex = imageMessages.findIndex(msg => msg.id === message.id);

    setLightboxImages(images);
    setLightboxIndex(Math.max(0, currentIndex));
    setShowLightbox(true);
  };

  // 处理文件下载
  const handleFileDownload = (fileUrl: string, fileName?: string) => {
    const a = document.createElement('a');
    a.href = fileUrl;
    a.download = fileName || 'download';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // 预览文件
  const previewChatFile = (message: ChatMessage) => {
    if (!message.fileUrl || !message.fileName) return;

    // 构造文件对象用于预览
    const fileForPreview = {
      id: message.id,
      name: message.fileName,
      type: getFileType(message.fileName),
      size: message.fileSize || 0,
      path: message.fileUrl,
      url: message.fileUrl
    };

    setPreviewFile(fileForPreview);
    setShowPreview(true);
  };



  // 格式化时间
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!chat) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-gray-500 dark:text-gray-400">无法加载聊天</div>
      </div>
    );
  }

  // 安全检查：对于非系统聊天，确保 otherParticipants 存在且不为空
  if (chat.type !== 'SYSTEM' && (!chat.otherParticipants || chat.otherParticipants.length === 0)) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-gray-500 dark:text-gray-400">聊天参与者信息不完整</div>
      </div>
    );
  }

  const otherParticipant = chat.type === 'SYSTEM' ? null : chat.otherParticipants[0];

  return (
    <div className="flex flex-col h-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      {/* 聊天头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          {chat?.type === 'SYSTEM' ? (
            <div className="w-8 h-8 rounded-full mr-3 bg-blue-500 flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          ) : otherParticipant ? (
            <div className="mr-3">
              <Avatar
                user={{
                  id: otherParticipant.id,
                  name: otherParticipant.name,
                  avatar: otherParticipant.avatar
                }}
                size="sm"
              />
            </div>
          ) : null}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">
              {chat?.type === 'SYSTEM' ? '系统通知' : otherParticipant?.name || '未知用户'}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {chat?.type === 'SYSTEM' ? '系统消息和通知' : otherParticipant?.email || ''}
            </p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-8">
            开始对话吧！
          </div>
        ) : (
          messages.map((message) => {
            const isOwn = message.sender?.id === session?.user?.id;
            const isSystem = message.isSystem || message.type === 'SYSTEM';

            // 系统消息显示为普通消息样式
            if (isSystem) {
              return (
                <div key={message.id} className="flex justify-start">
                  <div className="flex max-w-xs lg:max-w-md flex-row">
                    {/* 系统头像 */}
                    <div className="w-6 h-6 rounded-full flex-shrink-0 bg-blue-500 flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="mx-2 text-left">
                      <div className="inline-block px-3 py-2 rounded-lg text-sm bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                        <div className="whitespace-pre-line">{message.content}</div>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {formatTime(message.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>
              );
            }

            // 普通消息显示
            return (
              <div
                key={message.id}
                className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse' : 'flex-row'}`}>
                  {message.sender && (
                    <div className="flex-shrink-0">
                      <Avatar
                        user={{
                          id: message.sender.id,
                          name: message.sender.name,
                          avatar: message.sender.avatar
                        }}
                        size="xs"
                      />
                    </div>
                  )}
                  <div className={`mx-2 ${isOwn ? 'text-right' : 'text-left'}`}>
                    <div
                      className={`inline-block px-3 py-2 rounded-lg text-sm ${
                        isOwn
                          ? 'bg-primary-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                      }`}
                    >
                      {message.type === 'FILE' ? (
                        <WeChatStyleFileMessage
                          fileName={message.fileName || ''}
                          fileSize={message.fileSize || 0}
                          fileUrl={message.fileUrl || ''}
                          fileType={getFileType(message.fileName || '')}
                          isOwnMessage={isOwn}
                          onPreview={() => {
                            const fileType = getFileType(message.fileName || '');
                            if (fileType.startsWith('image/')) {
                              handleImagePreview(message);
                            } else {
                              previewChatFile(message);
                            }
                          }}
                          onDownload={() => handleFileDownload(message.fileUrl || '', message.fileName)}
                        />
                      ) : (
                        <EnhancedEmojiRenderer
                          text={message.content}
                          className="whitespace-pre-line"
                        />
                      )}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {formatTime(message.createdAt)}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 消息输入 - 系统聊天不显示输入框 */}
      {chat?.type !== 'SYSTEM' && (
        <form onSubmit={sendMessage} className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex space-x-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="输入消息..."
              className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-100"
              disabled={sending || uploading}
            />

            {/* 文件上传按钮 */}
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileUpload}
              className="hidden"
              accept="*/*"
            />
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading || sending}
              className="px-3 py-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              title="发送文件"
            >
              {uploading ? (
                <svg className="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              )}
            </button>

            {/* 表情选择器 */}
            <EnhancedEmojiPicker
              onEmojiSelect={handleEmojiSelect}
              disabled={sending || uploading}
            />

            <button
              type="submit"
              disabled={!newMessage.trim() || sending || uploading}
              className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sending ? (
                <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
            </button>
          </div>
        </form>
      )}

      {/* 图片预览 */}
      {showLightbox && (
        <ImageLightbox
          images={lightboxImages}
          currentIndex={lightboxIndex}
          onClose={() => setShowLightbox(false)}
          onNext={() => setLightboxIndex((prev) => (prev + 1) % lightboxImages.length)}
          onPrev={() => setLightboxIndex((prev) => (prev - 1 + lightboxImages.length) % lightboxImages.length)}
        />
      )}

      {/* 文件预览 */}
      {showPreview && previewFile && (
        <ChatFilePreview
          file={previewFile}
          onClose={() => {
            setShowPreview(false);
            setPreviewFile(null);
          }}
        />
      )}
    </div>
  );
}
