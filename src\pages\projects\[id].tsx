import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { format } from 'date-fns';
import TaskCard from '@/components/TaskCard';
import FileCard from '@/components/FileCard';
import EnhancedFileUpload from '@/components/EnhancedFileUpload';
import ProjectChat from '@/components/ProjectChat';
import TeamMemberCard from '@/components/TeamMemberCard';
import Avatar from '@/components/Avatar';
import { Project, User } from '@/types';

export default function ProjectDetail() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { id } = router.query;

  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'tasks', 'files', 'members', 'chat'
  const [isOwner, setIsOwner] = useState(false);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [addingMember, setAddingMember] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // 如果未登录，重定向到登录页
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 获取项目详情
  useEffect(() => {
    if (status === 'authenticated' && id) {
      fetchProjectDetails();
    }
  }, [status, id]);

  // 根据URL参数切换标签页
  useEffect(() => {
    const { tab } = router.query;
    if (tab && ['overview', 'tasks', 'files', 'members', 'chat'].includes(tab as string)) {
      setActiveTab(tab as string);
    }
  }, [router.query]);

  const fetchProjectDetails = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/projects/${id}`);

      if (!response.ok) {
        throw new Error('获取项目详情失败');
      }

      const data = await response.json();
      setProject(data);

      // 检查当前用户是否为项目所有者
      if (session?.user?.id === data.owner.id) {
        setIsOwner(true);
      }
    } catch (error) {
      console.error('获取项目详情失败:', error);
      setError('获取项目详情失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 获取可添加的团队成员列表
  const fetchAvailableUsers = async () => {
    if (!project) return;

    try {
      // 获取所有已审核的团队成员
      const response = await fetch('/api/users');
      if (response.ok) {
        const allUsers = await response.json();

        // 过滤出已审核的团队成员
        const teamMembers = allUsers.filter((user: any) => user.status === 'APPROVED');

        // 过滤掉已经是项目成员的用户
        const currentMemberIds = [
          project.owner.id,
          ...project.members.map((member: any) => member.id)
        ];

        // 可添加的团队成员 = 团队成员 - 当前项目成员
        const availableTeamMembers = teamMembers.filter((user: any) =>
          !currentMemberIds.includes(user.id)
        );

        setAvailableUsers(availableTeamMembers);
      } else {
        console.error('获取用户列表失败:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('获取团队成员列表失败:', error);
    }
  };

  // 添加成员
  const addMember = async (userId: string) => {
    setAddingMember(true);
    try {
      const response = await fetch(`/api/projects/${id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      if (response.ok) {
        // 重新获取项目详情以更新成员列表
        await fetchProjectDetails();
        setShowAddMemberModal(false);
      } else {
        const errorData = await response.json();
        setError(errorData.message || '添加成员失败');
      }
    } catch (error) {
      console.error('添加成员失败:', error);
      setError('添加成员失败，请稍后再试');
    } finally {
      setAddingMember(false);
    }
  };

  // 快速更新任务状态
  const handleTaskStatusChange = async (taskId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        // 重新获取项目详情以更新任务列表
        await fetchProjectDetails();
      } else {
        const errorData = await response.json();
        setError(errorData.message || '更新任务状态失败');
      }
    } catch (error) {
      console.error('更新任务状态失败:', error);
      setError('更新任务状态失败，请稍后再试');
    }
  };

  // 归档项目
  const archiveProject = async () => {
    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'ARCHIVED' }),
      });

      if (response.ok) {
        await fetchProjectDetails();
      } else {
        const errorData = await response.json();
        setError(errorData.message || '归档项目失败');
      }
    } catch (error) {
      console.error('归档项目失败:', error);
      setError('归档项目失败，请稍后再试');
    }
  };

  // 删除项目
  const deleteProject = async () => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/projects/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        router.push('/projects');
      } else {
        const errorData = await response.json();
        setError(errorData.message || '删除项目失败');
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      setError('删除项目失败，请稍后再试');
    } finally {
      setDeleting(false);
      setShowDeleteModal(false);
    }
  };

  // 格式化日期
  const formatDate = (date: any) => {
    if (!date) return '未设置';
    return format(new Date(date), 'yyyy-MM-dd');
  };

  // 获取项目状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return '规划中';
      case 'ACTIVE':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'ARCHIVED':
        return '已归档';
      default:
        return status;
    }
  };

  // 获取状态标签样式
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // 计算任务统计数据
  const getTaskStats = () => {
    if (!project || !project.tasks) return { todo: 0, inProgress: 0, review: 0, completed: 0 };

    return {
      todo: project.tasks.filter((task: any) => task.status === 'TODO').length,
      inProgress: project.tasks.filter((task: any) => task.status === 'IN_PROGRESS').length,
      review: project.tasks.filter((task: any) => task.status === 'REVIEW').length,
      completed: project.tasks.filter((task: any) => task.status === 'COMPLETED').length,
    };
  };

  // 计算成员任务统计数据
  const getMemberStats = () => {
    if (!project || !project.tasks || !project.members) return [];

    const memberStats: Record<string, { name: string; completedTasks: number; totalTasks: number }> = {};

    // 初始化成员统计
    project.members.forEach((member: any) => {
      memberStats[member.id] = {
        name: member.name,
        completedTasks: 0,
        totalTasks: 0,
      };
    });

    // 添加项目所有者
    memberStats[project.owner.id] = {
      name: project.owner.name,
      completedTasks: 0,
      totalTasks: 0,
    };

    // 统计任务
    project.tasks.forEach((task: any) => {
      if (task.assignee && memberStats[task.assignee.id]) {
        memberStats[task.assignee.id].totalTasks++;
        if (task.status === 'COMPLETED') {
          memberStats[task.assignee.id].completedTasks++;
        }
      }
    });

    // 转换为数组并过滤掉没有任务的成员
    return Object.values(memberStats).filter((member: any) => member.totalTasks > 0);
  };

  // 加载中或未认证状态
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 加载中
  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-800 dark:text-red-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
        <div className="mt-4">
          <Link href="/projects" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目列表
          </Link>
        </div>
      </div>
    );
  }

  // 项目不存在
  if (!project) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">项目不存在或您没有权限访问</span>
        </div>
        <div className="mt-4">
          <Link href="/projects" className="text-primary-600 dark:text-primary-400 hover:underline">
            返回项目列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* 项目标题和操作按钮 */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div>
          <div className="flex items-center">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mr-3">
              {project.title}
            </h1>
            <span className={`px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(project.status)}`}>
              {getStatusText(project.status)}
            </span>
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            负责人: {project.owner.name} | 创建于: {formatDate(project.createdAt)}
          </p>
        </div>

        {isOwner && (
          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <Link href={`/projects/${id}/edit`} className="btn btn-secondary">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              编辑
            </Link>
            <Link href={`/projects/${id}/tasks/new`} className="btn btn-primary">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              添加任务
            </Link>
            {project.status !== 'ARCHIVED' && (
              <button
                onClick={archiveProject}
                className="btn btn-secondary"
                title="归档项目"
              >
                <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l6 6 6-6" />
                </svg>
                归档
              </button>
            )}
            <button
              onClick={() => setShowDeleteModal(true)}
              className="btn btn-danger"
              title="删除项目"
            >
              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              删除
            </button>
          </div>
        )}
      </div>

      {/* 标签页导航 */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            概览
          </button>
          <button
            onClick={() => setActiveTab('tasks')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'tasks'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            任务 ({project.tasks.length})
          </button>
          <button
            onClick={() => setActiveTab('files')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'files'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            文件 ({project.files.length})
          </button>
          <button
            onClick={() => setActiveTab('members')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'members'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            成员 ({project.members.length + 1})
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={`py-4 px-1 text-center border-b-2 font-medium text-sm ${
              activeTab === 'chat'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600'
            }`}
          >
            项目讨论
          </button>
        </nav>
      </div>

      {/* 概览标签页 */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* 项目统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                  <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">总任务数</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{project.tasks.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                  <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">已完成</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{getTaskStats().completed}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                  <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">进行中</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{getTaskStats().inProgress}</p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                  <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">完成率</p>
                  <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">{Math.round(project.progress * 100)}%</p>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 项目信息 */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">项目信息</h2>

                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">描述</h3>
                    <p className="mt-1 text-gray-700 dark:text-gray-300">{project.description || '无描述'}</p>
                  </div>

                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">开始日期</h3>
                      <p className="mt-1 text-gray-700 dark:text-gray-300">{formatDate(project.startDate)}</p>
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">截止日期</h3>
                      <p className="mt-1 text-gray-700 dark:text-gray-300">{formatDate(project.endDate)}</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">整体进度</h3>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full transition-all duration-300 ${
                          project.progress >= 0.8 ? 'bg-green-500' :
                          project.progress >= 0.6 ? 'bg-yellow-500' :
                          project.progress >= 0.3 ? 'bg-orange-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${project.progress * 100}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <span>0%</span>
                      <span className="font-medium">{Math.round(project.progress * 100)}%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 任务状态分布和成员统计 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 任务状态分布 */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">任务状态分布</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                      <span className="text-2xl font-bold text-gray-600 dark:text-gray-400">{getTaskStats().todo}</span>
                    </div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">待处理</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                      <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">{getTaskStats().inProgress}</span>
                    </div>
                    <p className="text-sm font-medium text-blue-600 dark:text-blue-400">进行中</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                      <span className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{getTaskStats().review}</span>
                    </div>
                    <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">待审核</p>
                  </div>
                  <div className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                      <span className="text-2xl font-bold text-green-600 dark:text-green-400">{getTaskStats().completed}</span>
                    </div>
                    <p className="text-sm font-medium text-green-600 dark:text-green-400">已完成</p>
                  </div>
                </div>
              </div>

              {/* 成员任务完成情况 */}
              {getMemberStats().length > 0 && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                  <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">成员任务完成情况</h3>
                  <div className="space-y-4">
                    {getMemberStats().map((member: any, index) => {
                      const completionRate = member.totalTasks > 0 ? (member.completedTasks / member.totalTasks) * 100 : 0;
                      return (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div className="flex items-center">
                            <div className="w-10 h-10 rounded-full bg-primary-500 flex items-center justify-center text-white font-medium">
                              {member.name.charAt(0).toUpperCase()}
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{member.name}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {member.completedTasks}/{member.totalTasks} 任务完成
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="w-24 bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  completionRate >= 80 ? 'bg-green-500' :
                                  completionRate >= 60 ? 'bg-yellow-500' :
                                  completionRate >= 40 ? 'bg-orange-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${completionRate}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium text-gray-600 dark:text-gray-400 w-12 text-right">
                              {completionRate.toFixed(0)}%
                            </span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 任务标签页 */}
      {activeTab === 'tasks' && (
        <div className="space-y-6">
          {/* 任务筛选和统计 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  项目任务 ({project.tasks.length})
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  已完成 {getTaskStats().completed} 个，进行中 {getTaskStats().inProgress} 个
                </p>
              </div>
              {isOwner && (
                <div className="mt-4 sm:mt-0">
                  <Link href={`/projects/${id}/tasks/new`} className="btn btn-primary">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    添加任务
                  </Link>
                </div>
              )}
            </div>
          </div>

          {project.tasks.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {project.tasks.map((task: any) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onStatusChange={handleTaskStatusChange}
                  showQuickActions={true}
                />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">还没有任务</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                开始为这个项目创建任务，让团队成员知道需要完成什么工作。
              </p>
              {isOwner && (
                <Link href={`/projects/${id}/tasks/new`} className="btn btn-primary">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  创建第一个任务
                </Link>
              )}
            </div>
          )}
        </div>
      )}

      {/* 文件标签页 */}
      {activeTab === 'files' && (
        <div className="space-y-6">
          {/* 文件统计和操作 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  项目文件 ({project.files.length})
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  共享研究资料和文档
                </p>
              </div>
              <div className="mt-4 sm:mt-0 flex items-center space-x-3">
                <button
                  onClick={() => setShowFileUpload(!showFileUpload)}
                  className="btn btn-primary"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  {showFileUpload ? '隐藏上传' : '快速上传'}
                </button>
                <Link href={`/files/upload?projectId=${id}`} className="btn btn-secondary">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  高级上传
                </Link>
              </div>
            </div>
          </div>

          {/* 快速上传区域 */}
          {showFileUpload && (
            <div className="mb-6">
              <EnhancedFileUpload
                projectId={id as string}
                onUploadComplete={(file) => {
                  // 刷新项目详情以更新文件列表
                  fetchProjectDetails();
                }}
                onUploadError={(error) => {
                  setError(error);
                }}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
                maxFiles={5}
                maxSize={20 * 1024 * 1024} // 20MB
              />
            </div>
          )}

          {project.files.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {project.files.map((file: any) => (
                <FileCard key={file.id} file={file} />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                </svg>
              </div>
              <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">还没有文件</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                上传研究资料、文档和数据，与团队成员共享重要信息。
              </p>
              <Link href={`/files/upload?projectId=${id}`} className="btn btn-primary">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                上传第一个文件
              </Link>
            </div>
          )}
        </div>
      )}



      {/* 成员标签页 */}
      {activeTab === 'members' && (
        <div className="space-y-6">
          {/* 成员管理头部 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  项目成员 ({project.members.length + 1})
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  管理项目团队成员，查看成员贡献和任务分配
                </p>
              </div>
              {isOwner && (
                <button
                  onClick={() => {
                    fetchAvailableUsers();
                    setShowAddMemberModal(true);
                  }}
                  className="btn btn-primary"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  添加成员
                </button>
              )}
            </div>
          </div>

          {/* 成员列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* 项目负责人 */}
            {(() => {
              const ownerTasks = project.tasks.filter((task: any) => task.assignee?.id === project.owner.id);
              const completedTasks = ownerTasks.filter((task: any) => task.status === 'COMPLETED').length;
              const completionRate = ownerTasks.length > 0 ? (completedTasks / ownerTasks.length) * 100 : 0;

              const ownerMemberData = {
                id: project.owner.id,
                name: project.owner.name,
                email: project.owner.email,
                avatar: project.owner.avatar,
                role: 'LEADER',
                position: project.owner.position,
                department: project.owner.department,
                age: project.owner.age,
                bio: project.owner.bio,
                stats: {
                  totalProjects: 1,
                  activeProjects: project.status === 'ACTIVE' ? 1 : 0,
                  totalTasks: ownerTasks.length,
                  completedTasks: completedTasks,
                  completionRate: Math.round(completionRate),
                  activityScore: 90,
                  filesUploaded: project.files.filter((file: any) => file.uploaderId === project.owner.id).length,
                  messagesCount: 0,
                },
              };

              return (
                <div key={project.owner.id} className="relative">
                  <div className="absolute -top-2 -right-2 z-10">
                    <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                      负责人
                    </span>
                  </div>
                  <TeamMemberCard
                    member={ownerMemberData}
                    variant="detailed"
                  />
                </div>
              );
            })()}

            {/* 项目成员 */}
            {project.members.map((member: any) => {
              const memberTasks = project.tasks.filter((task: any) => task.assignee?.id === member.id);
              const completedTasks = memberTasks.filter((task: any) => task.status === 'COMPLETED').length;
              const completionRate = memberTasks.length > 0 ? (completedTasks / memberTasks.length) * 100 : 0;

              const memberData = {
                id: member.id,
                name: member.name,
                email: member.email,
                avatar: member.avatar,
                role: member.role || 'MEMBER',
                position: member.position,
                department: member.department,
                age: member.age,
                bio: member.bio,
                stats: {
                  totalProjects: 1,
                  activeProjects: 1,
                  totalTasks: memberTasks.length,
                  completedTasks: completedTasks,
                  completionRate: Math.round(completionRate),
                  contributionScore: memberTasks.length > 0 ? Math.min(100, memberTasks.length * 15 + completedTasks * 10) : 25,
                  filesUploaded: project.files.filter((file: any) => file.uploaderId === member.id).length,
                  messagesCount: 0,
                },
              };

              return (
                <TeamMemberCard
                  key={member.id}
                  member={memberData}
                  variant="detailed"
                />
              );
            })}
          </div>

          {/* 如果没有其他成员 */}
          {project.members.length === 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
              <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                项目团队待扩展
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                目前项目只有负责人，邀请更多成员加入可以提高协作效率
              </p>
              {isOwner && (
                <button
                  onClick={() => {
                    fetchAvailableUsers();
                    setShowAddMemberModal(true);
                  }}
                  className="btn btn-primary"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  邀请成员
                </button>
              )}
            </div>
          )}
        </div>
      )}

      {/* 聊天标签页 */}
      {activeTab === 'chat' && (
        <div className="space-y-4">
          {/* 项目讨论工具栏 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  项目讨论
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  与团队成员实时交流项目进展和想法
                </p>
              </div>
              <div className="flex items-center space-x-3">
                {/* 一键腾讯会议 */}
                <button
                  onClick={() => {
                    // 打开会议创建模态框，预填项目信息
                    const meetingSubject = `${project.title} - 项目讨论会议`;
                    const now = new Date();
                    const startTime = new Date(now.getTime() + 5 * 60000); // 5分钟后开始
                    const endTime = new Date(startTime.getTime() + 60 * 60000); // 1小时会议

                    // 这里可以调用会议创建API或打开会议创建模态框
                    window.open(`/team?meeting=true&subject=${encodeURIComponent(meetingSubject)}&start=${startTime.toISOString()}&end=${endTime.toISOString()}`, '_blank');
                  }}
                  className="btn btn-primary btn-sm"
                  title="创建项目讨论会议"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  视频会议
                </button>

                {/* 项目文件快速上传 */}
                <button
                  onClick={() => setActiveTab('files')}
                  className="btn btn-secondary btn-sm"
                  title="查看项目文件"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  项目文件
                </button>
              </div>
            </div>
          </div>

          {/* 聊天组件 - 固定高度 */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md" style={{ height: 'calc(100vh - 320px)', minHeight: '500px' }}>
            <ProjectChat
              projectId={project.id}
              projectTitle={project.title}
            />
          </div>
        </div>
      )}

      {/* 添加成员模态框 */}
      {showAddMemberModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    添加项目成员
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    从团队成员中选择要添加到此项目的成员
                  </p>
                </div>
                <button
                  onClick={() => setShowAddMemberModal(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {availableUsers.length === 0 ? (
                <div className="p-8 text-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  <h4 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    没有可添加的团队成员
                  </h4>
                  <p className="text-gray-500 dark:text-gray-400">
                    所有团队成员都已是此项目的成员，或者团队中暂无其他可添加的成员。
                  </p>
                </div>
              ) : (
                <div className="p-2">
                  <div className="mb-4 px-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      找到 {availableUsers.length} 位可添加的团队成员
                    </p>
                  </div>
                  {availableUsers.map((user: any) => (
                    <div
                      key={user.id}
                      className="mx-2 mb-2 p-4 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Avatar
                            user={{
                              id: user.id,
                              name: user.name,
                              avatar: user.avatar
                            }}
                            size="md"
                          />
                          <div className="ml-3">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {user.name}
                            </h4>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {user.email}
                            </p>
                            {user.position && (
                              <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                                {user.position} {user.department && `• ${user.department}`}
                              </p>
                            )}
                            <div className="flex items-center mt-1">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                user.role === 'ADMIN'
                                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                  : user.role === 'LEADER'
                                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                              }`}>
                                {user.role === 'ADMIN' ? '管理员' : user.role === 'LEADER' ? '项目负责人' : '团队成员'}
                              </span>
                              <span className="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                                已审核
                              </span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => addMember(user.id)}
                          disabled={addingMember}
                          className="btn btn-primary btn-sm flex-shrink-0"
                        >
                          {addingMember ? (
                            <>
                              <svg className="w-4 h-4 animate-spin mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              添加中...
                            </>
                          ) : (
                            <>
                              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              添加
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                💡 只有团队成员才能被添加到项目中。添加后，他们将能够查看项目内容、参与讨论并被分配任务
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 删除确认模态框 */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    确认删除项目
                  </h3>
                </div>
              </div>
              <div className="mb-6">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  您确定要删除项目 <strong>"{project.title}"</strong> 吗？
                </p>
                <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                  此操作不可撤销，将永久删除项目及其所有相关数据（任务、文件、消息等）。
                </p>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="btn btn-secondary"
                  disabled={deleting}
                >
                  取消
                </button>
                <button
                  onClick={deleteProject}
                  disabled={deleting}
                  className="btn btn-danger"
                >
                  {deleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      删除中...
                    </>
                  ) : (
                    '确认删除'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
