// app.js
import { apiService } from './services/api'
import { authService } from './services/auth'

App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:3000', // LabSync Web版本的API地址
    isLoggedIn: false,
    unreadCount: 0
  },

  onLaunch() {
    console.log('LabSync小程序启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 初始化API服务
    apiService.init(this.globalData.baseUrl)
    
    // 获取系统信息
    this.getSystemInfo()
  },

  onShow() {
    console.log('LabSync小程序显示')
    
    // 如果已登录，刷新用户信息
    if (this.globalData.isLoggedIn) {
      this.refreshUserInfo()
    }
  },

  onHide() {
    console.log('LabSync小程序隐藏')
  },

  onError(msg) {
    console.error('LabSync小程序错误:', msg)
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (token && userInfo) {
      this.globalData.token = token
      this.globalData.userInfo = userInfo
      this.globalData.isLoggedIn = true
      
      // 验证token是否有效
      this.validateToken()
    }
  },

  // 验证token有效性
  async validateToken() {
    try {
      const result = await authService.validateToken()
      if (!result.success) {
        this.logout()
      }
    } catch (error) {
      console.error('Token验证失败:', error)
      this.logout()
    }
  },

  // 刷新用户信息
  async refreshUserInfo() {
    try {
      const userInfo = await apiService.getCurrentUser()
      this.globalData.userInfo = userInfo
      wx.setStorageSync('userInfo', userInfo)
    } catch (error) {
      console.error('刷新用户信息失败:', error)
    }
  },

  // 获取系统信息
  getSystemInfo() {
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
        console.log('系统信息:', res)
      }
    })
  },

  // 登录
  login(token, userInfo) {
    this.globalData.token = token
    this.globalData.userInfo = userInfo
    this.globalData.isLoggedIn = true
    
    // 保存到本地存储
    wx.setStorageSync('token', token)
    wx.setStorageSync('userInfo', userInfo)
    
    console.log('用户登录成功:', userInfo.name)
  },

  // 登出
  logout() {
    this.globalData.token = null
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
    
    // 清除本地存储
    wx.removeStorageSync('token')
    wx.removeStorageSync('userInfo')
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
    
    console.log('用户已登出')
  },

  // 显示错误信息
  showError(message, title = '错误') {
    wx.showModal({
      title,
      content: message,
      showCancel: false,
      confirmText: '确定'
    })
  },

  // 显示成功信息
  showSuccess(message) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: 2000
    })
  },

  // 显示加载中
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    })
  },

  // 隐藏加载
  hideLoading() {
    wx.hideLoading()
  },

  // 更新未读消息数量
  updateUnreadCount(count) {
    this.globalData.unreadCount = count
    
    // 更新tabBar徽标
    if (count > 0) {
      wx.setTabBarBadge({
        index: 3, // 聊天tab的索引
        text: count > 99 ? '99+' : count.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 3
      })
    }
  }
})
