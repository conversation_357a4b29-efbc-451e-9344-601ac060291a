/* pages/index/index.wxss */

/* 用户信息卡片 */
.user-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info .avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  color: #6b7280;
}

.user-actions {
  display: flex;
  gap: 16rpx;
}

/* 搜索框 */
.search-box {
  margin-bottom: 20rpx;
}

.search-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #f9fafb;
  box-sizing: border-box;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.stat-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:active {
  transform: scale(0.95);
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 快速操作 */
.action-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f9fafb;
  border: none;
  border-radius: 16rpx;
  transition: background-color 0.2s;
}

.action-btn:active {
  background-color: #f3f4f6;
}

.action-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 24rpx;
  color: #374151;
}

/* 最近项目 */
.recent-section {
  margin-bottom: 20rpx;
}

.more-link {
  font-size: 26rpx;
  color: #3b82f6;
}

.project-list {
  margin-top: 20rpx;
}

.project-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s;
}

.project-item:last-child {
  border-bottom: none;
}

.project-item:active {
  background-color: #f9fafb;
}

.project-info {
  flex: 1;
}

.project-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.project-desc {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-meta {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.project-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #6b7280;
}

/* 最近任务 */
.task-list {
  margin-top: 20rpx;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:active {
  background-color: #f9fafb;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.task-meta {
  display: flex;
  gap: 20rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

.task-project {
  color: #3b82f6;
}

/* 最近消息 */
.message-list {
  margin-top: 20rpx;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
  transition: background-color 0.2s;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item:active {
  background-color: #f9fafb;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
}

.message-avatar image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-info {
  flex: 1;
}

.message-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.message-content {
  font-size: 26rpx;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.message-time {
  font-size: 24rpx;
  color: #9ca3af;
}

.message-badge {
  background-color: #ef4444;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}
