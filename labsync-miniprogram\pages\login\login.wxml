<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- Logo和标题 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <view class="title">LabSync</view>
    <view class="subtitle">实验室管理系统</view>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <!-- 登录方式切换 -->
    <view class="login-tabs" wx:if="{{loginType !== 'bind'}}">
      <view 
        class="tab-item {{loginType === 'account' ? 'active' : ''}}"
        data-type="account"
        bindtap="switchLoginType"
      >
        账号登录
      </view>
      <view 
        class="tab-item {{loginType === 'wechat' ? 'active' : ''}}"
        data-type="wechat"
        bindtap="switchLoginType"
      >
        微信登录
      </view>
    </view>

    <!-- 绑定提示 -->
    <view class="bind-tip" wx:if="{{loginType === 'bind'}}">
      <view class="tip-text">请输入您的LabSync账号信息来绑定微信</view>
      <button class="cancel-btn" bindtap="cancelBind">取消</button>
    </view>

    <!-- 账号登录表单 -->
    <view class="form-content" wx:if="{{loginType === 'account' || loginType === 'bind'}}">
      <view class="form-group">
        <view class="form-label">邮箱</view>
        <input 
          class="form-input"
          type="text"
          placeholder="请输入邮箱"
          value="{{email}}"
          bindinput="onEmailInput"
          maxlength="50"
        />
      </view>

      <view class="form-group">
        <view class="form-label">密码</view>
        <view class="password-input">
          <input 
            class="form-input"
            type="{{showPassword ? 'text' : 'password'}}"
            placeholder="请输入密码"
            value="{{password}}"
            bindinput="onPasswordInput"
            maxlength="20"
          />
          <view class="password-toggle" bindtap="togglePasswordVisibility">
            <text class="icon">{{showPassword ? '👁️' : '👁️‍🗨️'}}</text>
          </view>
        </view>
      </view>

      <view class="form-actions" wx:if="{{loginType === 'account'}}">
        <view class="forgot-password" bindtap="forgotPassword">忘记密码？</view>
      </view>

      <button 
        class="btn btn-primary btn-block {{loading ? 'btn-disabled' : ''}}"
        bindtap="handleLogin"
        disabled="{{loading}}"
      >
        {{loading ? '登录中...' : (loginType === 'bind' ? '绑定账号' : '登录')}}
      </button>

      <view class="register-link" wx:if="{{loginType === 'account'}}">
        <text>还没有账号？</text>
        <text class="link" bindtap="goToRegister">立即注册</text>
      </view>
    </view>

    <!-- 微信登录 -->
    <view class="wechat-login" wx:if="{{loginType === 'wechat'}}">
      <view class="wechat-icon">
        <image src="/images/wechat-icon.png" mode="aspectFit"></image>
      </view>
      <view class="wechat-text">使用微信账号快速登录</view>
      <button 
        class="btn btn-success btn-block {{loading ? 'btn-disabled' : ''}}"
        bindtap="handleLogin"
        disabled="{{loading}}"
      >
        {{loading ? '登录中...' : '微信登录'}}
      </button>
      <view class="wechat-tip">
        <text>首次使用需要绑定LabSync账号</text>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <view class="version">版本 1.0.0</view>
    <view class="copyright">© 2024 LabSync. All rights reserved.</view>
  </view>
</view>
