// pages/index/index.js
import { apiService } from '../../services/api'
import { authService } from '../../services/auth'
import { formatTime, calculateProgress } from '../../utils/util'

Page({
  data: {
    userInfo: null,
    stats: {
      projectCount: 0,
      taskCount: 0,
      completedTaskCount: 0,
      unreadMessageCount: 0
    },
    recentProjects: [],
    recentTasks: [],
    recentMessages: [],
    loading: true,
    refreshing: false
  },

  onLoad() {
    console.log('首页加载')
    this.checkLoginStatus()
  },

  onShow() {
    if (authService.isLoggedIn()) {
      this.loadDashboardData()
    }
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadDashboardData().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    if (!authService.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }

    this.setData({
      userInfo: authService.getCurrentUser()
    })

    this.loadDashboardData()
  },

  // 加载仪表板数据
  async loadDashboardData() {
    try {
      this.setData({ loading: true })

      // 并行加载数据
      const [projects, tasks, chats] = await Promise.all([
        this.loadRecentProjects(),
        this.loadRecentTasks(),
        this.loadRecentMessages()
      ])

      // 计算统计数据
      const stats = this.calculateStats(projects, tasks, chats)
      this.setData({ stats })

    } catch (error) {
      console.error('加载仪表板数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载最近项目
  async loadRecentProjects() {
    try {
      const projects = await apiService.getProjects()
      const recentProjects = projects
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 3)
        .map(project => ({
          ...project,
          progress: calculateProgress(project.tasks || []),
          updatedAtFormatted: formatTime(project.updatedAt)
        }))

      this.setData({ recentProjects })
      return projects
    } catch (error) {
      console.error('加载最近项目失败:', error)
      return []
    }
  },

  // 加载最近任务
  async loadRecentTasks() {
    try {
      const tasks = await apiService.getMyTasks()
      const recentTasks = tasks
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)
        .map(task => ({
          ...task,
          updatedAtFormatted: formatTime(task.updatedAt)
        }))

      this.setData({ recentTasks })
      return tasks
    } catch (error) {
      console.error('加载最近任务失败:', error)
      return []
    }
  },

  // 加载最近消息
  async loadRecentMessages() {
    try {
      const chats = await apiService.getChats()
      const recentMessages = chats
        .filter(chat => chat.messages && chat.messages.length > 0)
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 3)
        .map(chat => ({
          ...chat,
          lastMessage: chat.messages[0],
          updatedAtFormatted: formatTime(chat.updatedAt)
        }))

      this.setData({ recentMessages })
      return chats
    } catch (error) {
      console.error('加载最近消息失败:', error)
      return []
    }
  },

  // 计算统计数据
  calculateStats(projects, tasks, chats) {
    const completedTasks = tasks.filter(task => task.status === 'COMPLETED')
    const unreadMessages = chats.reduce((count, chat) => {
      return count + (chat.unreadCount || 0)
    }, 0)

    return {
      projectCount: projects.length,
      taskCount: tasks.length,
      completedTaskCount: completedTasks.length,
      unreadMessageCount: unreadMessages
    }
  },

  // 跳转到项目列表
  goToProjects() {
    wx.switchTab({
      url: '/pages/projects/projects'
    })
  },

  // 跳转到任务列表
  goToTasks() {
    wx.switchTab({
      url: '/pages/tasks/tasks'
    })
  },

  // 跳转到聊天列表
  goToChats() {
    wx.switchTab({
      url: '/pages/chats/chats'
    })
  },

  // 跳转到个人资料
  goToProfile() {
    wx.switchTab({
      url: '/pages/profile/profile'
    })
  },

  // 跳转到项目详情
  goToProjectDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/project-detail/project-detail?id=${id}`
    })
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?id=${id}`
    })
  },

  // 跳转到聊天详情
  goToChatDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/chat-detail/chat-detail?id=${id}`
    })
  },

  // 创建新项目
  createProject() {
    if (!authService.hasPermission('create_project')) {
      wx.showModal({
        title: '权限不足',
        content: '您没有创建项目的权限',
        showCancel: false
      })
      return
    }

    wx.navigateTo({
      url: '/pages/project-detail/project-detail?mode=create'
    })
  },

  // 创建新任务
  createTask() {
    wx.navigateTo({
      url: '/pages/task-detail/task-detail?mode=create'
    })
  },

  // 搜索
  onSearchInput(e) {
    const query = e.detail.value.trim()
    if (query) {
      wx.navigateTo({
        url: `/pages/search/search?q=${encodeURIComponent(query)}`
      })
    }
  },

  // 扫码功能
  scanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        // 处理扫码结果
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        wx.showToast({
          title: '扫码失败',
          icon: 'error'
        })
      }
    })
  },

  // 处理扫码结果
  handleScanResult(result) {
    try {
      // 尝试解析为JSON
      const data = JSON.parse(result)
      
      if (data.type === 'project' && data.id) {
        // 项目二维码
        wx.navigateTo({
          url: `/pages/project-detail/project-detail?id=${data.id}`
        })
      } else if (data.type === 'task' && data.id) {
        // 任务二维码
        wx.navigateTo({
          url: `/pages/task-detail/task-detail?id=${data.id}`
        })
      } else {
        wx.showToast({
          title: '无效的二维码',
          icon: 'error'
        })
      }
    } catch (error) {
      // 如果不是JSON格式，当作普通文本处理
      wx.showModal({
        title: '扫码结果',
        content: result,
        showCancel: false
      })
    }
  }
})
