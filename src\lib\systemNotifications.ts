import { prisma } from './prisma';

// 发送系统通知消息
export async function sendSystemNotification(userId: string, message: string) {
  try {
    // 查找或创建用户的系统通知聊天
    let systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: userId,
          },
        },
      },
    });

    if (!systemChat) {
      // 创建系统通知聊天
      systemChat = await prisma.chat.create({
        data: {
          type: 'SYSTEM',
          name: '系统通知',
          participants: {
            connect: { id: userId },
          },
        },
      });
    }

    // 发送系统消息
    const systemMessage = await prisma.chatMessage.create({
      data: {
        content: message,
        type: 'SYSTEM',
        isSystem: true,
        chatId: systemChat.id,
        // 系统消息不需要发送者
      },
    });

    // 更新聊天的最后更新时间
    await prisma.chat.update({
      where: { id: systemChat.id },
      data: { updatedAt: new Date() },
    });

    return systemMessage;
  } catch (error) {
    console.error('发送系统通知失败:', error);
    throw error;
  }
}

// 任务分配通知
export async function notifyTaskAssigned(taskId: string, assigneeId: string, assignerName: string, taskTitle: string, projectTitle: string) {
  const message = `📋 您被分配了新任务：「${taskTitle}」\n项目：${projectTitle}\n分配人：${assignerName}`;
  await sendSystemNotification(assigneeId, message);
}

// 任务完成通知
export async function notifyTaskCompleted(taskId: string, ownerId: string, completedByName: string, taskTitle: string, projectTitle: string) {
  const message = `✅ 任务已完成：「${taskTitle}」\n项目：${projectTitle}\n完成人：${completedByName}`;
  await sendSystemNotification(ownerId, message);
}

// 项目成员添加通知
export async function notifyProjectMemberAdded(projectId: string, memberId: string, projectTitle: string, addedByName: string) {
  const message = `👥 您被添加到项目：「${projectTitle}」\n添加人：${addedByName}`;
  await sendSystemNotification(memberId, message);
}

// 项目状态变更通知
export async function notifyProjectStatusChanged(projectId: string, memberIds: string[], projectTitle: string, newStatus: string, changedByName: string) {
  const message = `📊 项目状态已更新：「${projectTitle}」\n新状态：${newStatus}\n更新人：${changedByName}`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 文件上传通知
export async function notifyFileUploaded(projectId: string, memberIds: string[], fileName: string, projectTitle: string, uploaderName: string) {
  const message = `📎 新文件上传：「${fileName}」\n项目：${projectTitle}\n上传人：${uploaderName}`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 项目截止日期提醒
export async function notifyProjectDeadlineReminder(projectId: string, memberIds: string[], projectTitle: string, dueDate: Date) {
  const daysLeft = Math.ceil((dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  const message = `⏰ 项目截止日期提醒：「${projectTitle}」\n剩余时间：${daysLeft} 天`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 任务截止日期提醒
export async function notifyTaskDeadlineReminder(taskId: string, assigneeIds: string[], taskTitle: string, projectTitle: string, dueDate: Date) {
  const daysLeft = Math.ceil((dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  const message = `⏰ 任务截止日期提醒：「${taskTitle}」\n项目：${projectTitle}\n剩余时间：${daysLeft} 天`;

  for (const assigneeId of assigneeIds) {
    await sendSystemNotification(assigneeId, message);
  }
}

// 用户审核通知
export async function notifyUserApproved(userId: string, approverName: string) {
  const message = `🎉 您的账号已通过审核！\n审核人：${approverName}\n您现在可以正常使用系统的所有功能了。`;
  await sendSystemNotification(userId, message);
}

export async function notifyUserRejected(userId: string, approverName: string, reason?: string) {
  const reasonText = reason ? `\n拒绝原因：${reason}` : '';
  const message = `❌ 您的账号审核未通过\n审核人：${approverName}${reasonText}\n如有疑问，请联系管理员。`;
  await sendSystemNotification(userId, message);
}

// 项目结题通知
export async function notifyProjectCompleted(projectId: string, memberIds: string[], projectTitle: string, completedByName: string) {
  const message = `🏆 项目已结题：「${projectTitle}」\n结题人：${completedByName}\n感谢您的参与和贡献！`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 项目归档通知
export async function notifyProjectArchived(projectId: string, memberIds: string[], projectTitle: string, archivedByName: string) {
  const message = `📦 项目已归档：「${projectTitle}」\n归档人：${archivedByName}\n项目相关资料已保存至归档区。`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 项目进度重要节点通知
export async function notifyProjectMilestone(projectId: string, memberIds: string[], projectTitle: string, milestone: string, progress: number) {
  const message = `🎯 项目进度更新：「${projectTitle}」\n${milestone}\n当前进度：${Math.round(progress * 100)}%`;

  for (const memberId of memberIds) {
    await sendSystemNotification(memberId, message);
  }
}

// 项目成员移除通知
export async function notifyProjectMemberRemoved(projectId: string, memberId: string, projectTitle: string, removedByName: string) {
  const message = `👋 您已被移出项目：「${projectTitle}」\n操作人：${removedByName}`;
  await sendSystemNotification(memberId, message);
}
