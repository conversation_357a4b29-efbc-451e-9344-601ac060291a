import type { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs';
import path from 'path';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    await isAuthenticated(req, res, async () => {});
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);
  const { id: fileId } = req.query;

  if (!fileId || typeof fileId !== 'string') {
    return res.status(400).json({ message: '无效的文件ID' });
  }

  try {
    // 获取文件信息
    const file = await prisma.file.findUnique({
      where: { id: fileId },
      include: {
        project: {
          select: {
            id: true,
            ownerId: true,
            members: {
              select: { id: true }
            }
          }
        },
        uploader: {
          select: { id: true }
        }
      }
    });

    if (!file) {
      return res.status(404).json({ message: '文件不存在' });
    }

    // 权限检查
    const hasAccess = 
      file.uploader.id === userId || // 上传者
      (file.project && (
        file.project.ownerId === userId || // 项目负责人
        file.project.members.some(member => member.id === userId) // 项目成员
      ));

    if (!hasAccess) {
      return res.status(403).json({ message: '无权限下载此文件' });
    }

    // 构建文件路径
    const filePath = path.join(process.cwd(), 'uploads', file.path);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: '文件不存在于服务器' });
    }

    // 获取文件统计信息
    const stats = fs.statSync(filePath);

    // 设置响应头强制下载
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(file.name)}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // 创建文件流并发送
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      console.error('文件下载错误:', error);
      if (!res.headersSent) {
        res.status(500).json({ message: '文件下载失败' });
      }
    });

    // 记录下载日志（可选）
    console.log(`用户 ${userId} 下载了文件 ${file.name} (${file.id})`);

  } catch (error) {
    console.error('文件下载失败:', error);
    return res.status(500).json({ message: '文件下载失败' });
  }
}
