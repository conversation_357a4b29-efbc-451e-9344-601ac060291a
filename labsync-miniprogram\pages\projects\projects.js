// pages/projects/projects.js
import { apiService } from '../../services/api'
import { authService } from '../../services/auth'
import { formatTime, calculateProgress, formatProjectStatus, getProjectStatusColor } from '../../utils/util'

Page({
  data: {
    projects: [],
    filteredProjects: [],
    loading: true,
    refreshing: false,
    searchQuery: '',
    filterStatus: 'ALL', // ALL, PLANNING, ACTIVE, COMPLETED, ARCHIVED
    sortBy: 'updatedAt', // updatedAt, title, progress
    sortOrder: 'desc', // asc, desc
    showFilter: false,
    stats: {
      total: 0,
      active: 0,
      completed: 0,
      planning: 0
    }
  },

  onLoad() {
    console.log('项目页面加载')
    this.checkLoginStatus()
  },

  onShow() {
    if (authService.isLoggedIn()) {
      this.loadProjects()
    }
  },

  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadProjects().finally(() => {
      this.setData({ refreshing: false })
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    // 可以在这里实现分页加载
    console.log('到达底部')
  },

  // 检查登录状态
  checkLoginStatus() {
    if (!authService.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
  },

  // 加载项目列表
  async loadProjects() {
    try {
      this.setData({ loading: true })

      const projects = await apiService.getProjects()
      
      // 处理项目数据
      const processedProjects = projects.map(project => ({
        ...project,
        progress: calculateProgress(project.tasks || []),
        statusText: formatProjectStatus(project.status),
        statusColor: getProjectStatusColor(project.status),
        updatedAtFormatted: formatTime(project.updatedAt),
        memberCount: project.members ? project.members.length : 0,
        taskCount: project.tasks ? project.tasks.length : 0
      }))

      // 计算统计数据
      const stats = this.calculateStats(processedProjects)

      this.setData({
        projects: processedProjects,
        stats
      })

      // 应用过滤和排序
      this.applyFiltersAndSort()

    } catch (error) {
      console.error('加载项目失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 计算统计数据
  calculateStats(projects) {
    return {
      total: projects.length,
      active: projects.filter(p => p.status === 'ACTIVE').length,
      completed: projects.filter(p => p.status === 'COMPLETED').length,
      planning: projects.filter(p => p.status === 'PLANNING').length
    }
  },

  // 应用过滤和排序
  applyFiltersAndSort() {
    let filtered = [...this.data.projects]

    // 搜索过滤
    if (this.data.searchQuery) {
      const query = this.data.searchQuery.toLowerCase()
      filtered = filtered.filter(project => 
        project.title.toLowerCase().includes(query) ||
        (project.description && project.description.toLowerCase().includes(query))
      )
    }

    // 状态过滤
    if (this.data.filterStatus !== 'ALL') {
      filtered = filtered.filter(project => project.status === this.data.filterStatus)
    }

    // 排序
    filtered.sort((a, b) => {
      const { sortBy, sortOrder } = this.data
      let aValue = a[sortBy]
      let bValue = b[sortBy]

      // 处理日期排序
      if (sortBy === 'updatedAt') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // 处理字符串排序
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    this.setData({ filteredProjects: filtered })
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchQuery: e.detail.value.trim()
    })
    this.applyFiltersAndSort()
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchQuery: ''
    })
    this.applyFiltersAndSort()
  },

  // 切换过滤器显示
  toggleFilter() {
    this.setData({
      showFilter: !this.data.showFilter
    })
  },

  // 状态过滤
  onStatusFilter(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      filterStatus: status,
      showFilter: false
    })
    this.applyFiltersAndSort()
  },

  // 排序
  onSort(e) {
    const { sortby, order } = e.currentTarget.dataset
    this.setData({
      sortBy: sortby,
      sortOrder: order,
      showFilter: false
    })
    this.applyFiltersAndSort()
  },

  // 跳转到项目详情
  goToProjectDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/project-detail/project-detail?id=${id}`
    })
  },

  // 创建新项目
  createProject() {
    if (!authService.hasPermission('create_project')) {
      wx.showModal({
        title: '权限不足',
        content: '您没有创建项目的权限',
        showCancel: false
      })
      return
    }

    wx.navigateTo({
      url: '/pages/project-detail/project-detail?mode=create'
    })
  },

  // 项目操作菜单
  showProjectActions(e) {
    const { id, title } = e.currentTarget.dataset
    const project = this.data.projects.find(p => p.id === id)
    
    if (!project) return

    const actions = ['查看详情', '编辑项目']
    
    // 检查删除权限
    if (authService.hasPermission('delete_project') || 
        (project.ownerId === authService.getCurrentUser().id)) {
      actions.push('删除项目')
    }

    wx.showActionSheet({
      itemList: actions,
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.goToProjectDetail({ currentTarget: { dataset: { id } } })
            break
          case 1:
            wx.navigateTo({
              url: `/pages/project-detail/project-detail?id=${id}&mode=edit`
            })
            break
          case 2:
            this.confirmDeleteProject(id, title)
            break
        }
      }
    })
  },

  // 确认删除项目
  confirmDeleteProject(id, title) {
    wx.showModal({
      title: '删除项目',
      content: `确定要删除项目"${title}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ef4444',
      success: (res) => {
        if (res.confirm) {
          this.deleteProject(id)
        }
      }
    })
  },

  // 删除项目
  async deleteProject(id) {
    try {
      wx.showLoading({ title: '删除中...' })

      await apiService.deleteProject(id)

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })

      // 重新加载项目列表
      this.loadProjects()

    } catch (error) {
      console.error('删除项目失败:', error)
      wx.showModal({
        title: '删除失败',
        content: error.message || '删除项目失败',
        showCancel: false
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 分享项目
  shareProject(e) {
    const { id, title } = e.currentTarget.dataset
    
    // 生成项目分享链接
    const shareData = {
      type: 'project',
      id: id,
      title: title
    }

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    // 可以在这里实现更复杂的分享逻辑
    console.log('分享项目:', shareData)
  }
})
