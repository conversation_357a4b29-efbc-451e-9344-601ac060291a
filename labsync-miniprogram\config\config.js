// config/config.js
// 配置文件

// 环境配置
const ENV = {
  // 开发环境
  development: {
    baseUrl: 'http://localhost:3000',
    apiTimeout: 10000,
    debug: true
  },
  
  // 生产环境
  production: {
    baseUrl: 'https://your-domain.com', // 替换为实际的生产环境域名
    apiTimeout: 15000,
    debug: false
  }
}

// 当前环境 (development | production)
const CURRENT_ENV = 'development'

// 导出当前环境配置
export const config = ENV[CURRENT_ENV]

// API配置
export const API_CONFIG = {
  baseUrl: config.baseUrl,
  timeout: config.apiTimeout,
  
  // API路径
  endpoints: {
    // 认证相关
    login: '/api/auth/login',
    register: '/api/auth/register',
    validate: '/api/auth/validate',
    wechatLogin: '/api/auth/wechat',
    bindWechat: '/api/auth/bind-wechat',
    
    // 用户相关
    users: '/api/users',
    currentUser: '/api/users/me',
    
    // 项目相关
    projects: '/api/projects',
    
    // 任务相关
    tasks: '/api/tasks',
    
    // 聊天相关
    chats: '/api/chats',
    
    // 文件相关
    files: '/api/files',
    upload: '/api/upload',
    
    // 通知相关
    notifications: '/api/notifications',
    
    // 搜索
    search: '/api/search'
  }
}

// 小程序配置
export const MINIPROGRAM_CONFIG = {
  // 版本信息
  version: '1.0.0',
  
  // 功能开关
  features: {
    wechatLogin: true,
    fileUpload: true,
    pushNotification: true,
    darkMode: false
  },
  
  // 界面配置
  ui: {
    primaryColor: '#3b82f6',
    successColor: '#10b981',
    warningColor: '#f59e0b',
    errorColor: '#ef4444',
    
    // 分页配置
    pageSize: 20,
    
    // 文件上传限制
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: [
      'image/jpeg', 'image/png', 'image/gif',
      'application/pdf', 'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]
  },
  
  // 缓存配置
  cache: {
    userInfoExpiry: 24 * 60 * 60 * 1000, // 24小时
    projectListExpiry: 5 * 60 * 1000, // 5分钟
    taskListExpiry: 3 * 60 * 1000 // 3分钟
  }
}

// 错误码配置
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // 权限错误
  FORBIDDEN: 'FORBIDDEN',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // 数据错误
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DUPLICATE_ERROR: 'DUPLICATE_ERROR',
  
  // 服务器错误
  SERVER_ERROR: 'SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
}

// 错误消息映射
export const ERROR_MESSAGES = {
  [ERROR_CODES.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ERROR_CODES.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ERROR_CODES.UNAUTHORIZED]: '登录已过期，请重新登录',
  [ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
  [ERROR_CODES.INVALID_CREDENTIALS]: '邮箱或密码错误',
  [ERROR_CODES.FORBIDDEN]: '权限不足，无法执行此操作',
  [ERROR_CODES.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [ERROR_CODES.NOT_FOUND]: '请求的资源不存在',
  [ERROR_CODES.VALIDATION_ERROR]: '输入数据格式错误',
  [ERROR_CODES.DUPLICATE_ERROR]: '数据已存在',
  [ERROR_CODES.SERVER_ERROR]: '服务器内部错误',
  [ERROR_CODES.SERVICE_UNAVAILABLE]: '服务暂时不可用，请稍后重试'
}

// 状态配置
export const STATUS_CONFIG = {
  // 项目状态
  projectStatus: {
    PLANNING: { text: '计划中', color: '#f59e0b' },
    ACTIVE: { text: '进行中', color: '#10b981' },
    COMPLETED: { text: '已完成', color: '#3b82f6' },
    ARCHIVED: { text: '已归档', color: '#6b7280' }
  },
  
  // 任务状态
  taskStatus: {
    TODO: { text: '待办', color: '#6b7280' },
    IN_PROGRESS: { text: '进行中', color: '#f59e0b' },
    REVIEW: { text: '待审核', color: '#8b5cf6' },
    COMPLETED: { text: '已完成', color: '#10b981' }
  },
  
  // 任务优先级
  taskPriority: {
    LOW: { text: '低', color: '#10b981' },
    MEDIUM: { text: '中', color: '#f59e0b' },
    HIGH: { text: '高', color: '#f97316' },
    URGENT: { text: '紧急', color: '#ef4444' }
  },
  
  // 用户角色
  userRole: {
    ADMIN: { text: '管理员', color: '#8b5cf6' },
    LEADER: { text: '项目负责人', color: '#3b82f6' },
    MEMBER: { text: '团队成员', color: '#10b981' },
    GUEST: { text: '访客', color: '#6b7280' }
  },
  
  // 用户状态
  userStatus: {
    PENDING: { text: '待审核', color: '#f59e0b' },
    APPROVED: { text: '已通过', color: '#10b981' },
    REJECTED: { text: '已拒绝', color: '#ef4444' }
  }
}

// 默认配置
export const DEFAULT_CONFIG = {
  // 默认头像
  defaultAvatar: '/images/default-avatar.png',
  
  // 默认项目封面
  defaultProjectCover: '/images/default-project.png',
  
  // 分页默认值
  defaultPageSize: 20,
  
  // 搜索防抖延迟
  searchDebounceDelay: 300,
  
  // 自动保存延迟
  autoSaveDelay: 2000,
  
  // 消息轮询间隔
  messagePollingInterval: 30000, // 30秒
  
  // 通知显示时长
  toastDuration: 2000,
  
  // 加载超时时间
  loadingTimeout: 10000
}

// 导出所有配置
export default {
  config,
  API_CONFIG,
  MINIPROGRAM_CONFIG,
  ERROR_CODES,
  ERROR_MESSAGES,
  STATUS_CONFIG,
  DEFAULT_CONFIG
}
