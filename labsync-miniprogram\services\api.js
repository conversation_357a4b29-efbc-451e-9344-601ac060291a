// services/api.js
// API服务层，与LabSync Web版本的API进行通信

class ApiService {
  constructor() {
    this.baseUrl = ''
    this.timeout = 10000
  }

  // 初始化API服务
  init(baseUrl) {
    this.baseUrl = baseUrl
  }

  // 获取请求头
  getHeaders() {
    const app = getApp()
    const headers = {
      'Content-Type': 'application/json'
    }
    
    if (app.globalData.token) {
      headers['Authorization'] = `Bearer ${app.globalData.token}`
    }
    
    return headers
  }

  // 通用请求方法
  request(options) {
    const app = getApp()
    
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${this.baseUrl}${options.url}`,
        method: options.method || 'GET',
        data: options.data || {},
        header: this.getHeaders(),
        timeout: this.timeout,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data)
          } else if (res.statusCode === 401) {
            // 未授权，清除登录状态
            app.logout()
            reject(new Error('登录已过期，请重新登录'))
          } else {
            reject(new Error(res.data.message || '请求失败'))
          }
        },
        fail: (error) => {
          console.error('API请求失败:', error)
          reject(new Error('网络请求失败，请检查网络连接'))
        }
      })
    })
  }

  // GET请求
  get(url, data) {
    return this.request({
      url,
      method: 'GET',
      data
    })
  }

  // POST请求
  post(url, data) {
    return this.request({
      url,
      method: 'POST',
      data
    })
  }

  // PUT请求
  put(url, data) {
    return this.request({
      url,
      method: 'PUT',
      data
    })
  }

  // DELETE请求
  delete(url, data) {
    return this.request({
      url,
      method: 'DELETE',
      data
    })
  }

  // 用户相关API
  async getCurrentUser() {
    return this.get('/api/users/me')
  }

  async updateUser(data) {
    return this.put('/api/users/me', data)
  }

  async getUsers() {
    return this.get('/api/users')
  }

  // 项目相关API
  async getProjects() {
    return this.get('/api/projects')
  }

  async getProject(id) {
    return this.get(`/api/projects/${id}`)
  }

  async createProject(data) {
    return this.post('/api/projects', data)
  }

  async updateProject(id, data) {
    return this.put(`/api/projects/${id}`, data)
  }

  async deleteProject(id) {
    return this.delete(`/api/projects/${id}`)
  }

  async getProjectMembers(id) {
    return this.get(`/api/projects/${id}/members`)
  }

  async addProjectMember(id, userId) {
    return this.post(`/api/projects/${id}/members`, { userId })
  }

  async removeProjectMember(id, userId) {
    return this.delete(`/api/projects/${id}/members`, { userId })
  }

  // 任务相关API
  async getTasks(projectId) {
    const params = projectId ? { projectId } : {}
    return this.get('/api/tasks', params)
  }

  async getTask(id) {
    return this.get(`/api/tasks/${id}`)
  }

  async createTask(data) {
    return this.post('/api/tasks', data)
  }

  async updateTask(id, data) {
    return this.put(`/api/tasks/${id}`, data)
  }

  async deleteTask(id) {
    return this.delete(`/api/tasks/${id}`)
  }

  async getMyTasks() {
    return this.get('/api/tasks', { myTasks: true })
  }

  // 聊天相关API
  async getChats() {
    return this.get('/api/chats')
  }

  async getChat(id) {
    return this.get(`/api/chats/${id}`)
  }

  async createChat(data) {
    return this.post('/api/chats', data)
  }

  async getChatMessages(chatId, page = 1, limit = 20) {
    return this.get(`/api/chats/${chatId}/messages`, { page, limit })
  }

  async sendMessage(chatId, data) {
    return this.post(`/api/chats/${chatId}/messages`, data)
  }

  async markMessagesAsRead(chatId) {
    return this.post(`/api/chats/${chatId}/read`)
  }

  // 文件相关API
  async getFiles(projectId) {
    const params = projectId ? { projectId } : {}
    return this.get('/api/files', params)
  }

  async deleteFile(id) {
    return this.delete(`/api/files/${id}`)
  }

  // 通知相关API
  async getNotifications() {
    return this.get('/api/notifications')
  }

  async markNotificationAsRead(id) {
    return this.put(`/api/notifications/${id}/read`)
  }

  async markAllNotificationsAsRead() {
    return this.put('/api/notifications/read-all')
  }

  // 搜索API
  async search(query, type) {
    return this.get('/api/search', { q: query, type })
  }

  // 文件上传
  uploadFile(filePath, formData = {}) {
    const app = getApp()
    
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: `${this.baseUrl}/api/upload`,
        filePath,
        name: 'file',
        formData,
        header: {
          'Authorization': app.globalData.token ? `Bearer ${app.globalData.token}` : ''
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data)
            if (res.statusCode === 200) {
              resolve(data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (error) {
            reject(new Error('上传响应解析失败'))
          }
        },
        fail: (error) => {
          console.error('文件上传失败:', error)
          reject(new Error('文件上传失败'))
        }
      })
    })
  }

  // 下载文件
  downloadFile(url, fileName) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: `${this.baseUrl}${url}`,
        header: this.getHeaders(),
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath)
          } else {
            reject(new Error('下载失败'))
          }
        },
        fail: (error) => {
          console.error('文件下载失败:', error)
          reject(new Error('文件下载失败'))
        }
      })
    })
  }
}

// 导出单例
export const apiService = new ApiService()
