import React, { useState, useRef, useEffect } from 'react';
import EmojiPickerReact, { EmojiClickData, Theme } from 'emoji-picker-react';
import { FaceSmileIcon } from '@heroicons/react/24/outline';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  className?: string;
  buttonClassName?: string;
  disabled?: boolean;
}

export default function EmojiPicker({ 
  onEmojiSelect, 
  className = '',
  buttonClassName = '',
  disabled = false 
}: EmojiPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const pickerRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // 检测暗色主题
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'));
    };
    
    checkDarkMode();
    
    // 监听主题变化
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });
    
    return () => observer.disconnect();
  }, []);

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        pickerRef.current && 
        !pickerRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleEmojiClick = (emojiData: EmojiClickData) => {
    onEmojiSelect(emojiData.emoji);
    setIsOpen(false);
  };

  const togglePicker = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* 表情按钮 */}
      <button
        ref={buttonRef}
        type="button"
        onClick={togglePicker}
        disabled={disabled}
        className={`
          p-2 rounded-full transition-all duration-200 hover:scale-110
          ${disabled 
            ? 'opacity-50 cursor-not-allowed' 
            : 'text-gray-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-gray-100 dark:hover:bg-gray-700'
          }
          ${buttonClassName}
        `}
        title="添加表情"
      >
        <FaceSmileIcon className="w-5 h-5" />
      </button>

      {/* 表情选择器 */}
      {isOpen && (
        <div 
          ref={pickerRef}
          className="absolute bottom-full right-0 mb-2 z-50 shadow-2xl rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600"
          style={{ 
            transform: 'translateX(50%)',
            right: '50%'
          }}
        >
          <EmojiPickerReact
            onEmojiClick={handleEmojiClick}
            theme={isDarkMode ? Theme.DARK : Theme.LIGHT}
            width={320}
            height={400}
            searchDisabled={false}
            skinTonesDisabled={false}
            previewConfig={{
              defaultEmoji: '1f60a',
              defaultCaption: '选择一个表情',
              showPreview: true
            }}
            categories={[
              {
                name: 'smileys_people',
                category: 'Smileys & People'
              },
              {
                name: 'animals_nature', 
                category: 'Animals & Nature'
              },
              {
                name: 'food_drink',
                category: 'Food & Drink'
              },
              {
                name: 'travel_places',
                category: 'Travel & Places'
              },
              {
                name: 'activities',
                category: 'Activities'
              },
              {
                name: 'objects',
                category: 'Objects'
              },
              {
                name: 'symbols',
                category: 'Symbols'
              },
              {
                name: 'flags',
                category: 'Flags'
              }
            ]}
          />
        </div>
      )}
    </div>
  );
}

// 常用表情快捷组件
export function QuickEmojis({ 
  onEmojiSelect, 
  className = '' 
}: { 
  onEmojiSelect: (emoji: string) => void;
  className?: string;
}) {
  const quickEmojis = ['😀', '😂', '😍', '🤔', '😢', '😡', '👍', '👎', '❤️', '🎉'];

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {quickEmojis.map((emoji, index) => (
        <button
          key={index}
          type="button"
          onClick={() => onEmojiSelect(emoji)}
          className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors text-lg"
          title={`添加 ${emoji}`}
        >
          {emoji}
        </button>
      ))}
    </div>
  );
}

// 表情渲染组件
export function EmojiRenderer({ 
  text, 
  className = '' 
}: { 
  text: string;
  className?: string;
}) {
  // 简单的表情渲染，将 :emoji: 格式转换为实际表情
  const emojiMap: Record<string, string> = {
    ':smile:': '😀',
    ':laugh:': '😂',
    ':heart_eyes:': '😍',
    ':thinking:': '🤔',
    ':cry:': '😢',
    ':angry:': '😡',
    ':thumbs_up:': '👍',
    ':thumbs_down:': '👎',
    ':heart:': '❤️',
    ':party:': '🎉'
  };

  let renderedText = text;
  Object.entries(emojiMap).forEach(([code, emoji]) => {
    renderedText = renderedText.replace(new RegExp(code, 'g'), emoji);
  });

  return <span className={className}>{renderedText}</span>;
}
