{"name": "labsync", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:seed": "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^5.7.1", "@tailwindcss/postcss7-compat": "^2.2.17", "@types/formidable": "^3.4.5", "autoprefixer": "^9.8.8", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "daisyui": "^5.0.35", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "emoji-mart": "^5.6.0", "emoji-picker-react": "^4.12.2", "formidable": "^3.5.4", "gsap": "^3.13.0", "multer": "^1.4.5-lts.1", "next": "^14.0.4", "next-auth": "^4.24.11", "postcss": "^7.0.39", "prisma": "^5.7.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-chat-elements": "^12.0.14", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-file-icon": "^1.5.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-image-lightbox": "^5.1.4", "tailwindcss": "^2.2.19", "ts-node": "^10.9.2", "yet-another-react-lightbox": "^3.23.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}