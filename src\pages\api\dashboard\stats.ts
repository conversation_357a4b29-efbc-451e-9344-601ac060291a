import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId, isAdmin } from '@/lib/auth';
import { requireApprovedUser, handleApiError, sendSuccess, sendError } from '@/lib/apiMiddleware';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 使用中间件检查用户权限
  try {
    await requireApprovedUser(req, res, async () => {
      // 中间件通过后继续执行
    });
  } catch (error) {
    return; // 中间件已经处理了错误响应
  }

  const userId = await getCurrentUserId(req, res);
  const isUserAdmin = await isAdmin(req, res);

  // 验证用户ID
  if (!userId) {
    return sendError(res, '用户未认证', 'USER_NOT_AUTHENTICATED', 401);
  }

  // 处理GET请求 - 获取仪表盘统计数据
  if (req.method === 'GET') {
    try {
      let projectStats, taskStats, projectDetails;

      if (isUserAdmin) {
        // 管理员可以查看所有数据
        projectStats = await prisma.project.groupBy({
          by: ['status'],
          _count: {
            id: true,
          },
        });

        taskStats = await prisma.task.groupBy({
          by: ['status'],
          _count: {
            id: true,
          },
        });

        // 获取项目详情（包含任务统计）
        projectDetails = await prisma.project.findMany({
          select: {
            id: true,
            title: true,
            status: true,
            _count: {
              select: {
                tasks: true,
              },
            },
            tasks: {
              select: {
                status: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 6, // 只取前6个项目用于图表显示
        });
      } else {
        // 普通用户只能查看自己相关的数据
        projectStats = await prisma.project.groupBy({
          by: ['status'],
          where: {
            OR: [
              { ownerId: userId },
              {
                members: {
                  some: {
                    id: userId,
                  },
                },
              },
            ],
          },
          _count: {
            id: true,
          },
        });

        taskStats = await prisma.task.groupBy({
          by: ['status'],
          where: {
            OR: [
              { assigneeId: userId },
              {
                project: {
                  OR: [
                    { ownerId: userId },
                    {
                      members: {
                        some: {
                          id: userId,
                        },
                      },
                    },
                  ],
                },
              },
            ],
          },
          _count: {
            id: true,
          },
        });

        // 获取用户相关的项目详情
        projectDetails = await prisma.project.findMany({
          where: {
            OR: [
              { ownerId: userId },
              {
                members: {
                  some: {
                    id: userId,
                  },
                },
              },
            ],
          },
          select: {
            id: true,
            title: true,
            status: true,
            _count: {
              select: {
                tasks: true,
              },
            },
            tasks: {
              select: {
                status: true,
              },
            },
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 6,
        });
      }

      // 处理项目统计数据
      const projectStatsMap = projectStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id;
        return acc;
      }, {} as Record<string, number>);

      // 处理任务统计数据
      const taskStatsMap = taskStats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id;
        return acc;
      }, {} as Record<string, number>);

      // 获取额外的统计数据
      let userCount = 0, fileCount = 0, chatCount = 0, messageCount = 0;

      try {
        if (isUserAdmin) {
          // 管理员可以查看所有数据
          userCount = await prisma.user.count({
            where: {
              status: 'APPROVED'
            }
          });
          fileCount = await prisma.file.count();

          // 统计活跃聊天（有消息的聊天）
          chatCount = await prisma.chat.count({
            where: {
              messages: {
                some: {}
              }
            }
          });

          // 统计聊天消息和项目消息的总数
          const chatMessageCount = await prisma.chatMessage.count({
            where: {
              isSystem: false // 排除系统消息
            }
          });
          const projectMessageCount = await prisma.message.count();
          messageCount = chatMessageCount + projectMessageCount;
        } else {
          // 普通用户查看相关数据
          userCount = await prisma.user.count({
            where: {
              AND: [
                { status: 'APPROVED' },
                {
                  OR: [
                    { id: userId },
                    {
                      projects: {
                        some: {
                          OR: [
                            { ownerId: userId },
                            {
                              members: {
                                some: {
                                  id: userId,
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  ],
                }
              ]
            },
          });

          fileCount = await prisma.file.count({
            where: {
              OR: [
                { uploaderId: userId },
                {
                  project: {
                    OR: [
                      { ownerId: userId },
                      {
                        members: {
                          some: {
                            id: userId,
                          },
                        },
                      },
                    ],
                  },
                },
              ],
            },
          });

          // 统计用户参与的活跃聊天
          chatCount = await prisma.chat.count({
            where: {
              AND: [
                {
                  participants: {
                    some: {
                      id: userId,
                    },
                  },
                },
                {
                  messages: {
                    some: {}
                  }
                }
              ]
            },
          });

          // 统计用户相关的聊天消息和项目消息
          const userChatMessageCount = await prisma.chatMessage.count({
            where: {
              AND: [
                { isSystem: false },
                {
                  chat: {
                    participants: {
                      some: {
                        id: userId,
                      },
                    },
                  },
                }
              ]
            },
          });

          const userProjectMessageCount = await prisma.message.count({
            where: {
              project: {
                OR: [
                  { ownerId: userId },
                  {
                    members: {
                      some: {
                        id: userId,
                      },
                    },
                  },
                ],
              },
            },
          });

          messageCount = userChatMessageCount + userProjectMessageCount;
        }
      } catch (statsError) {
        console.warn('获取统计数据时出现错误:', statsError);
        // 使用默认值，不影响主要功能
      }

      // 计算统计数据
      const totalProjects = Object.values(projectStatsMap).reduce((sum: number, count: any) => sum + count, 0);
      const activeProjects = (projectStatsMap['ACTIVE'] || 0) + (projectStatsMap['PLANNING'] || 0);
      const completedTasks = taskStatsMap['COMPLETED'] || 0;
      const inProgressTasks = taskStatsMap['IN_PROGRESS'] || 0;
      const totalTasks = Object.values(taskStatsMap).reduce((sum: number, count: any) => sum + count, 0);
      const pendingTasks = (taskStatsMap['TODO'] || 0) + (taskStatsMap['REVIEW'] || 0);

      // 处理项目详情数据用于图表
      const chartData = {
        labels: projectDetails.map(project => project.title),
        datasets: [
          {
            label: '已完成任务',
            data: projectDetails.map(project =>
              project.tasks.filter(task => task.status === 'COMPLETED').length
            ),
            backgroundColor: 'rgba(14, 165, 233, 0.7)',
          },
          {
            label: '进行中任务',
            data: projectDetails.map(project =>
              project.tasks.filter(task => task.status === 'IN_PROGRESS').length
            ),
            backgroundColor: 'rgba(249, 115, 22, 0.7)',
          },
          {
            label: '待处理任务',
            data: projectDetails.map(project =>
              project.tasks.filter(task => ['TODO', 'REVIEW'].includes(task.status)).length
            ),
            backgroundColor: 'rgba(168, 85, 247, 0.7)',
          },
        ],
      };

      // 获取最近活动（最近更新的任务）
      const recentActivities = await prisma.task.findMany({
        where: isUserAdmin ? {} : {
          OR: [
            { assigneeId: userId },
            {
              project: {
                OR: [
                  { ownerId: userId },
                  {
                    members: {
                      some: {
                        id: userId,
                      },
                    },
                  },
                ],
              },
            },
          ],
        },
        select: {
          id: true,
          title: true,
          status: true,
          updatedAt: true,
          project: {
            select: {
              title: true,
            },
          },
        },
        orderBy: {
          updatedAt: 'desc',
        },
        take: 5,
      });

      return sendSuccess(res, {
        stats: {
          totalProjects,
          activeProjects,
          completedTasks,
          inProgressTasks,
          totalTasks,
          pendingTasks,
          totalUsers: userCount,
          totalFiles: fileCount,
          projectFiles: fileCount, // 简化处理，假设所有文件都是项目文件
          activeChats: chatCount,
          totalMessages: messageCount,
        },
        chartData,
        recentActivities,
        projectDetails: projectDetails.map(project => {
          const totalTasks = project._count.tasks;
          const completedTasks = project.tasks.filter(task => task.status === 'COMPLETED' || task.status === 'DONE').length;
          const progress = totalTasks > 0 ? completedTasks / totalTasks : 0;

          return {
            id: project.id,
            title: project.title,
            status: project.status,
            totalTasks,
            completedTasks,
            inProgressTasks: project.tasks.filter(task => task.status === 'IN_PROGRESS').length,
            pendingTasks: project.tasks.filter(task => ['TODO', 'REVIEW'].includes(task.status)).length,
            progress,
          };
        }),
      }, '仪表盘数据获取成功');
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      return handleApiError(error, req, res);
    }
  }

  // 如果不是支持的HTTP方法
  return sendError(res, '方法不允许', 'METHOD_NOT_ALLOWED', 405);
}
