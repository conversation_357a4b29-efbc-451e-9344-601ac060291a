# LabSync 微信小程序部署指南

本文档详细说明如何部署和配置LabSync微信小程序。

## 📋 部署前准备

### 1. 微信小程序账号
- 注册微信小程序账号：https://mp.weixin.qq.com/
- 获取小程序AppID
- 配置服务器域名白名单

### 2. 后端服务
- 确保LabSync Web版本后端服务正常运行
- 配置HTTPS域名（生产环境必需）
- 开启CORS跨域支持

### 3. 开发工具
- 下载微信开发者工具：https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html
- 安装Node.js 14.0+

## 🔧 配置步骤

### 1. 修改配置文件

#### 更新API地址
编辑 `config/config.js`：
```javascript
const ENV = {
  production: {
    baseUrl: 'https://your-domain.com', // 替换为实际域名
    apiTimeout: 15000,
    debug: false
  }
}

// 切换到生产环境
const CURRENT_ENV = 'production'
```

#### 更新小程序AppID
编辑 `project.config.json`：
```json
{
  "appid": "wx1234567890abcdef", // 替换为实际AppID
  "projectname": "LabSync"
}
```

### 2. 配置服务器域名

在微信公众平台后台配置以下域名：

#### request合法域名
```
https://your-domain.com
```

#### uploadFile合法域名
```
https://your-domain.com
```

#### downloadFile合法域名
```
https://your-domain.com
```

### 3. 后端API适配

#### 添加微信登录接口
在LabSync后端添加微信登录API：

```javascript
// pages/api/auth/wechat.js
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { code } = req.body

  try {
    // 使用code换取openid和session_key
    const response = await fetch(`https://api.weixin.qq.com/sns/jscode2session?appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}&js_code=${code}&grant_type=authorization_code`)
    const data = await response.json()

    if (data.errcode) {
      return res.status(400).json({ message: '微信登录失败' })
    }

    // 查找或创建用户
    let user = await prisma.user.findFirst({
      where: { wechatOpenId: data.openid }
    })

    if (!user) {
      return res.status(404).json({ 
        success: false,
        message: '账号未绑定，请先绑定LabSync账号' 
      })
    }

    // 生成JWT token
    const token = jwt.sign({ userId: user.id }, JWT_SECRET)

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar
      }
    })
  } catch (error) {
    res.status(500).json({ message: '服务器错误' })
  }
}
```

#### 添加账号绑定接口
```javascript
// pages/api/auth/bind-wechat.js
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { email, password, code } = req.body

  try {
    // 验证用户账号密码
    const user = await prisma.user.findUnique({
      where: { email }
    })

    if (!user || !bcrypt.compareSync(password, user.password)) {
      return res.status(401).json({ message: '邮箱或密码错误' })
    }

    // 获取微信openid
    const wechatResponse = await fetch(`https://api.weixin.qq.com/sns/jscode2session?appid=${WECHAT_APPID}&secret=${WECHAT_SECRET}&js_code=${code}&grant_type=authorization_code`)
    const wechatData = await wechatResponse.json()

    if (wechatData.errcode) {
      return res.status(400).json({ message: '微信授权失败' })
    }

    // 绑定微信openid
    await prisma.user.update({
      where: { id: user.id },
      data: { wechatOpenId: wechatData.openid }
    })

    // 生成token
    const token = jwt.sign({ userId: user.id }, JWT_SECRET)

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar
      }
    })
  } catch (error) {
    res.status(500).json({ message: '服务器错误' })
  }
}
```

### 4. 数据库更新

添加微信相关字段到User模型：

```prisma
model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  password      String
  wechatOpenId  String?   @unique // 新增微信OpenID字段
  // ... 其他字段
}
```

运行数据库迁移：
```bash
npx prisma migrate dev --name add_wechat_openid
```

## 🚀 部署流程

### 1. 开发环境测试
```bash
# 1. 启动后端服务
cd LabSync-main
npm run dev

# 2. 打开微信开发者工具
# 3. 导入labsync-miniprogram项目
# 4. 配置AppID
# 5. 预览测试
```

### 2. 生产环境部署

#### 后端部署
```bash
# 1. 部署后端到服务器
cd LabSync-main
npm run build
npm start

# 2. 配置HTTPS证书
# 3. 配置域名解析
```

#### 小程序发布
```bash
# 1. 切换到生产配置
# 编辑 config/config.js，设置 CURRENT_ENV = 'production'

# 2. 在微信开发者工具中上传代码
# 点击"上传"按钮，填写版本号和项目备注

# 3. 在微信公众平台提交审核
# 登录 https://mp.weixin.qq.com/
# 进入版本管理，提交审核

# 4. 审核通过后发布
```

## ⚙️ 环境配置

### 开发环境
```javascript
// config/config.js
const ENV = {
  development: {
    baseUrl: 'http://localhost:3000',
    apiTimeout: 10000,
    debug: true
  }
}
const CURRENT_ENV = 'development'
```

### 生产环境
```javascript
// config/config.js
const ENV = {
  production: {
    baseUrl: 'https://labsync.your-domain.com',
    apiTimeout: 15000,
    debug: false
  }
}
const CURRENT_ENV = 'production'
```

## 🔒 安全配置

### 1. HTTPS配置
- 生产环境必须使用HTTPS
- 配置SSL证书
- 强制HTTPS重定向

### 2. 域名白名单
在微信公众平台配置：
- request合法域名
- uploadFile合法域名
- downloadFile合法域名

### 3. 接口安全
- 验证请求来源
- 实现接口限流
- 添加请求签名验证

## 📊 监控和日志

### 1. 错误监控
```javascript
// app.js
App({
  onError(msg) {
    console.error('小程序错误:', msg)
    // 可以接入第三方错误监控服务
  }
})
```

### 2. 性能监控
```javascript
// 监控页面加载时间
Page({
  onLoad() {
    const startTime = Date.now()
    // 页面加载逻辑
    const loadTime = Date.now() - startTime
    console.log('页面加载时间:', loadTime)
  }
})
```

### 3. 用户行为统计
```javascript
// 统计用户操作
const trackEvent = (event, data) => {
  console.log('用户行为:', event, data)
  // 可以接入第三方统计服务
}
```

## 🔧 常见问题

### 1. 网络请求失败
- 检查域名是否在白名单中
- 确认后端服务是否正常
- 检查HTTPS证书是否有效

### 2. 登录失败
- 确认AppID和AppSecret配置正确
- 检查微信登录接口实现
- 验证数据库连接

### 3. 文件上传失败
- 检查uploadFile域名配置
- 确认文件大小限制
- 验证文件类型限制

### 4. 页面白屏
- 检查页面路径配置
- 确认页面文件完整性
- 查看控制台错误信息

## 📞 技术支持

如遇到部署问题，请：
1. 查看微信开发者工具控制台错误
2. 检查后端服务日志
3. 提交Issue到GitHub仓库
4. 联系项目维护者

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础功能模块
- 支持微信登录
- 与Web版本数据互通

---

**部署成功后，用户就可以通过微信小程序便捷地使用LabSync实验室管理系统了！** 🎉
