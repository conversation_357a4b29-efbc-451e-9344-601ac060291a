import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    await isAuthenticated(req, res, async () => {});
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);

  try {
    // 获取用户的系统通知聊天
    const systemChat = await prisma.chat.findFirst({
      where: {
        type: 'SYSTEM',
        participants: {
          some: {
            id: userId,
          },
        },
      },
    });

    if (systemChat) {
      // 将该聊天中的所有系统消息标记为已读
      await prisma.chatMessage.updateMany({
        where: {
          chatId: systemChat.id,
          isSystem: true,
          isRead: false,
        },
        data: {
          isRead: true,
        },
      });
    }

    return res.status(200).json({ message: '全部标记已读成功' });
  } catch (error) {
    console.error('标记全部已读失败:', error);
    return res.status(500).json({ message: '标记全部已读失败' });
  }
}
