// services/auth.js
// 认证服务，处理登录、注册等认证相关功能

import { apiService } from './api'

class AuthService {
  constructor() {
    this.loginUrl = '/api/auth/login'
    this.registerUrl = '/api/auth/register'
    this.validateUrl = '/api/auth/validate'
  }

  // 用户登录
  async login(email, password) {
    try {
      const response = await apiService.post(this.loginUrl, {
        email,
        password
      })

      if (response.success) {
        const app = getApp()
        app.login(response.token, response.user)
        return {
          success: true,
          user: response.user,
          token: response.token
        }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 用户注册
  async register(userData) {
    try {
      const response = await apiService.post(this.registerUrl, userData)

      if (response.success) {
        return {
          success: true,
          message: response.message || '注册成功，请等待管理员审核'
        }
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  // 验证token有效性
  async validateToken() {
    try {
      const response = await apiService.get(this.validateUrl)
      return {
        success: response.valid,
        user: response.user
      }
    } catch (error) {
      console.error('Token验证失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 微信登录
  async wechatLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: async (res) => {
          if (res.code) {
            try {
              // 发送code到后端进行微信登录
              const response = await apiService.post('/api/auth/wechat', {
                code: res.code
              })

              if (response.success) {
                const app = getApp()
                app.login(response.token, response.user)
                resolve({
                  success: true,
                  user: response.user,
                  token: response.token
                })
              } else {
                reject(new Error(response.message || '微信登录失败'))
              }
            } catch (error) {
              reject(error)
            }
          } else {
            reject(new Error('获取微信登录code失败'))
          }
        },
        fail: (error) => {
          console.error('微信登录失败:', error)
          reject(new Error('微信登录失败'))
        }
      })
    })
  }

  // 获取微信用户信息
  async getWechatUserInfo() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve(res.userInfo)
        },
        fail: (error) => {
          console.error('获取微信用户信息失败:', error)
          reject(new Error('获取用户信息失败'))
        }
      })
    })
  }

  // 绑定微信账号
  async bindWechat(email, password) {
    try {
      // 先获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取微信授权失败')
      }

      // 发送绑定请求
      const response = await apiService.post('/api/auth/bind-wechat', {
        email,
        password,
        code: loginRes.code
      })

      if (response.success) {
        const app = getApp()
        app.login(response.token, response.user)
        return {
          success: true,
          user: response.user,
          token: response.token
        }
      } else {
        throw new Error(response.message || '绑定失败')
      }
    } catch (error) {
      console.error('绑定微信失败:', error)
      throw error
    }
  }

  // 登出
  logout() {
    const app = getApp()
    app.logout()
  }

  // 检查是否已登录
  isLoggedIn() {
    const app = getApp()
    return app.globalData.isLoggedIn
  }

  // 获取当前用户信息
  getCurrentUser() {
    const app = getApp()
    return app.globalData.userInfo
  }

  // 获取当前用户token
  getToken() {
    const app = getApp()
    return app.globalData.token
  }

  // 检查用户权限
  hasPermission(permission) {
    const user = this.getCurrentUser()
    if (!user) return false

    // 管理员拥有所有权限
    if (user.role === 'ADMIN') return true

    // 根据权限类型检查
    switch (permission) {
      case 'create_project':
        return user.role === 'LEADER' || user.role === 'ADMIN'
      case 'manage_users':
        return user.role === 'ADMIN'
      case 'delete_project':
        return user.role === 'LEADER' || user.role === 'ADMIN'
      default:
        return true
    }
  }

  // 检查用户状态
  isUserApproved() {
    const user = this.getCurrentUser()
    return user && user.status === 'APPROVED'
  }

  // 格式化用户角色
  formatUserRole(role) {
    const roleMap = {
      'ADMIN': '管理员',
      'LEADER': '项目负责人',
      'MEMBER': '团队成员',
      'GUEST': '访客'
    }
    return roleMap[role] || role
  }

  // 格式化用户状态
  formatUserStatus(status) {
    const statusMap = {
      'PENDING': '待审核',
      'APPROVED': '已通过',
      'REJECTED': '已拒绝'
    }
    return statusMap[status] || status
  }
}

// 导出单例
export const authService = new AuthService()
