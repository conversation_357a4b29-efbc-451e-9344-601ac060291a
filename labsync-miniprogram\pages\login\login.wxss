/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.title {
  font-size: 48rpx;
  font-weight: 700;
  color: #fff;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  width: 100%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  background-color: #f3f4f6;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 40rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #6b7280;
  transition: all 0.2s;
}

.tab-item.active {
  background-color: #fff;
  color: #3b82f6;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 绑定提示 */
.bind-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fef3c7;
  border: 1rpx solid #f59e0b;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #92400e;
  flex: 1;
}

.cancel-btn {
  background-color: transparent;
  border: 1rpx solid #f59e0b;
  color: #f59e0b;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
}

/* 表单内容 */
.form-content {
  width: 100%;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: #3b82f6;
  outline: none;
}

.password-input {
  position: relative;
}

.password-input .form-input {
  padding-right: 80rpx;
}

.password-toggle {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  padding: 8rpx;
}

.password-toggle .icon {
  font-size: 32rpx;
  color: #6b7280;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 40rpx;
}

.forgot-password {
  font-size: 26rpx;
  color: #3b82f6;
}

.register-link {
  text-align: center;
  margin-top: 30rpx;
  font-size: 26rpx;
  color: #6b7280;
}

.register-link .link {
  color: #3b82f6;
  margin-left: 8rpx;
}

/* 微信登录 */
.wechat-login {
  text-align: center;
}

.wechat-icon {
  margin-bottom: 30rpx;
}

.wechat-icon image {
  width: 120rpx;
  height: 120rpx;
}

.wechat-text {
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 40rpx;
}

.wechat-tip {
  margin-top: 30rpx;
  font-size: 24rpx;
  color: #9ca3af;
}

/* 底部 */
.footer {
  margin-top: 80rpx;
  text-align: center;
}

.version {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 12rpx;
}

.copyright {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.5);
}
