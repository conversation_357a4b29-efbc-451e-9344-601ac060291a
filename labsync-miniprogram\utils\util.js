// utils/util.js
// 通用工具函数

// 格式化时间
export function formatTime(date) {
  if (!date) return ''
  
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚'
  }
  
  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000))
    return `${minutes}分钟前`
  }
  
  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000))
    return `${hours}小时前`
  }
  
  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000))
    return `${days}天前`
  }
  
  // 超过7天显示具体日期
  const year = target.getFullYear()
  const month = target.getMonth() + 1
  const day = target.getDate()
  
  if (year === now.getFullYear()) {
    return `${month}月${day}日`
  } else {
    return `${year}年${month}月${day}日`
  }
}

// 格式化日期
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

// 格式化文件大小
export function formatFileSize(bytes) {
  if (!bytes || bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 获取文件扩展名
export function getFileExtension(filename) {
  if (!filename) return ''
  return filename.split('.').pop().toLowerCase()
}

// 获取文件类型图标
export function getFileIcon(filename) {
  const ext = getFileExtension(filename)
  const iconMap = {
    // 图片
    jpg: 'image',
    jpeg: 'image',
    png: 'image',
    gif: 'image',
    webp: 'image',
    svg: 'image',
    
    // 文档
    pdf: 'pdf',
    doc: 'word',
    docx: 'word',
    xls: 'excel',
    xlsx: 'excel',
    ppt: 'powerpoint',
    pptx: 'powerpoint',
    txt: 'text',
    
    // 压缩包
    zip: 'archive',
    rar: 'archive',
    '7z': 'archive',
    
    // 代码
    js: 'code',
    html: 'code',
    css: 'code',
    json: 'code',
    xml: 'code',
    
    // 音频
    mp3: 'audio',
    wav: 'audio',
    
    // 视频
    mp4: 'video',
    avi: 'video',
    mov: 'video'
  }
  
  return iconMap[ext] || 'file'
}

// 验证邮箱格式
export function validateEmail(email) {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return re.test(email)
}

// 验证手机号格式
export function validatePhone(phone) {
  const re = /^1[3-9]\d{9}$/
  return re.test(phone)
}

// 生成随机字符串
export function generateRandomString(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 防抖函数
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 深拷贝
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 获取项目状态颜色
export function getProjectStatusColor(status) {
  const colorMap = {
    'PLANNING': '#f59e0b',
    'ACTIVE': '#10b981',
    'COMPLETED': '#3b82f6',
    'ARCHIVED': '#6b7280'
  }
  return colorMap[status] || '#6b7280'
}

// 获取任务状态颜色
export function getTaskStatusColor(status) {
  const colorMap = {
    'TODO': '#6b7280',
    'IN_PROGRESS': '#f59e0b',
    'REVIEW': '#8b5cf6',
    'COMPLETED': '#10b981'
  }
  return colorMap[status] || '#6b7280'
}

// 获取任务优先级颜色
export function getTaskPriorityColor(priority) {
  const colorMap = {
    'LOW': '#10b981',
    'MEDIUM': '#f59e0b',
    'HIGH': '#f97316',
    'URGENT': '#ef4444'
  }
  return colorMap[priority] || '#6b7280'
}

// 格式化项目状态
export function formatProjectStatus(status) {
  const statusMap = {
    'PLANNING': '计划中',
    'ACTIVE': '进行中',
    'COMPLETED': '已完成',
    'ARCHIVED': '已归档'
  }
  return statusMap[status] || status
}

// 格式化任务状态
export function formatTaskStatus(status) {
  const statusMap = {
    'TODO': '待办',
    'IN_PROGRESS': '进行中',
    'REVIEW': '待审核',
    'COMPLETED': '已完成'
  }
  return statusMap[status] || status
}

// 格式化任务优先级
export function formatTaskPriority(priority) {
  const priorityMap = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return priorityMap[priority] || priority
}

// 计算项目进度
export function calculateProgress(tasks) {
  if (!tasks || tasks.length === 0) return 0
  
  const completedTasks = tasks.filter(task => task.status === 'COMPLETED')
  return Math.round((completedTasks.length / tasks.length) * 100)
}

// 获取用户头像
export function getUserAvatar(user) {
  if (user && user.avatar) {
    return user.avatar
  }
  // 返回默认头像
  return '/images/default-avatar.png'
}

// 截断文本
export function truncateText(text, maxLength = 50) {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// 检查是否为图片文件
export function isImageFile(filename) {
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
  const ext = getFileExtension(filename)
  return imageExts.includes(ext)
}

// 检查是否为视频文件
export function isVideoFile(filename) {
  const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
  const ext = getFileExtension(filename)
  return videoExts.includes(ext)
}

// 检查是否为音频文件
export function isAudioFile(filename) {
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg']
  const ext = getFileExtension(filename)
  return audioExts.includes(ext)
}

// 存储数据到本地
export function setStorage(key, data) {
  try {
    wx.setStorageSync(key, data)
    return true
  } catch (error) {
    console.error('存储数据失败:', error)
    return false
  }
}

// 从本地获取数据
export function getStorage(key, defaultValue = null) {
  try {
    return wx.getStorageSync(key) || defaultValue
  } catch (error) {
    console.error('获取数据失败:', error)
    return defaultValue
  }
}

// 删除本地数据
export function removeStorage(key) {
  try {
    wx.removeStorageSync(key)
    return true
  } catch (error) {
    console.error('删除数据失败:', error)
    return false
  }
}
