// pages/login/login.js
import { authService } from '../../services/auth'
import { validateEmail } from '../../utils/util'

Page({
  data: {
    email: '',
    password: '',
    loading: false,
    showPassword: false,
    loginType: 'account', // account | wechat
    canIUseGetUserProfile: false
  },

  onLoad() {
    // 检查是否支持getUserProfile
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }

    // 如果已经登录，跳转到首页
    if (authService.isLoggedIn()) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }
  },

  // 输入邮箱
  onEmailInput(e) {
    this.setData({
      email: e.detail.value.trim()
    })
  },

  // 输入密码
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 切换登录方式
  switchLoginType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      loginType: type
    })
  },

  // 账号密码登录
  async handleAccountLogin() {
    const { email, password } = this.data

    // 验证输入
    if (!email) {
      wx.showToast({
        title: '请输入邮箱',
        icon: 'error'
      })
      return
    }

    if (!validateEmail(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'error'
      })
      return
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'error'
      })
      return
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码至少6位',
        icon: 'error'
      })
      return
    }

    try {
      this.setData({ loading: true })

      const result = await authService.login(email, password)

      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 跳转到首页
        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
      }
    } catch (error) {
      console.error('登录失败:', error)
      wx.showModal({
        title: '登录失败',
        content: error.message || '登录失败，请检查邮箱和密码',
        showCancel: false
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 微信登录
  async handleWechatLogin() {
    try {
      this.setData({ loading: true })

      // 先尝试直接微信登录
      const result = await authService.wechatLogin()

      if (result.success) {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      
      // 如果微信登录失败，可能是账号未绑定，提示用户绑定
      wx.showModal({
        title: '账号未绑定',
        content: '您的微信账号尚未绑定LabSync账号，是否前往绑定？',
        confirmText: '去绑定',
        success: (res) => {
          if (res.confirm) {
            this.showBindDialog()
          }
        }
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 显示绑定对话框
  showBindDialog() {
    wx.showModal({
      title: '绑定账号',
      content: '请输入您的LabSync邮箱和密码来绑定微信账号',
      confirmText: '去绑定',
      success: (res) => {
        if (res.confirm) {
          // 切换到账号登录模式，用于绑定
          this.setData({
            loginType: 'bind'
          })
        }
      }
    })
  },

  // 绑定微信账号
  async handleBindWechat() {
    const { email, password } = this.data

    // 验证输入
    if (!email || !password) {
      wx.showToast({
        title: '请输入邮箱和密码',
        icon: 'error'
      })
      return
    }

    if (!validateEmail(email)) {
      wx.showToast({
        title: '邮箱格式不正确',
        icon: 'error'
      })
      return
    }

    try {
      this.setData({ loading: true })

      const result = await authService.bindWechat(email, password)

      if (result.success) {
        wx.showToast({
          title: '绑定成功',
          icon: 'success'
        })

        setTimeout(() => {
          wx.reLaunch({
            url: '/pages/index/index'
          })
        }, 1500)
      }
    } catch (error) {
      console.error('绑定失败:', error)
      wx.showModal({
        title: '绑定失败',
        content: error.message || '绑定失败，请检查邮箱和密码',
        showCancel: false
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 登录按钮点击
  handleLogin() {
    if (this.data.loading) return

    switch (this.data.loginType) {
      case 'account':
        this.handleAccountLogin()
        break
      case 'wechat':
        this.handleWechatLogin()
        break
      case 'bind':
        this.handleBindWechat()
        break
    }
  },

  // 跳转到注册页面
  goToRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    })
  },

  // 忘记密码
  forgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系管理员重置密码',
      showCancel: false
    })
  },

  // 取消绑定
  cancelBind() {
    this.setData({
      loginType: 'account',
      email: '',
      password: ''
    })
  }
})
