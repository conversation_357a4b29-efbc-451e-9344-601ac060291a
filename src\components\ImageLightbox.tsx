import React, { useState } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';
import {
  XMarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

interface ImageLightboxProps {
  images: Array<{
    src: string;
    alt?: string;
    title?: string;
  }>;
  currentIndex: number;
  onClose: () => void;
  onNext?: () => void;
  onPrev?: () => void;
  onDownload?: (src: string, filename?: string) => void;
}

export default function ImageLightbox({
  images,
  currentIndex,
  onClose,
  onNext,
  onPrev,
  onDownload
}: ImageLightboxProps) {
  const [zoom, setZoom] = useState(1);

  const handleDownload = () => {
    if (images[currentIndex] && onDownload) {
      onDownload(images[currentIndex].src, images[currentIndex].title);
    }
  };

  const slides = images.map(image => ({
    src: image.src,
    alt: image.alt,
    title: image.title
  }));

  return (
    <Lightbox
      open={true}
      close={onClose}
      slides={slides}
      index={currentIndex}
      on={{
        view: ({ index }) => {
          // 当索引改变时调用相应的回调
          if (index > currentIndex && onNext) {
            onNext();
          } else if (index < currentIndex && onPrev) {
            onPrev();
          }
        }
      }}
      render={{
        buttonPrev: onPrev ? () => (
          <button
            onClick={onPrev}
            className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
          >
            <ArrowLeftIcon className="w-6 h-6" />
          </button>
        ) : undefined,
        buttonNext: onNext ? () => (
          <button
            onClick={onNext}
            className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
          >
            <ArrowRightIcon className="w-6 h-6" />
          </button>
        ) : undefined,
        buttonClose: () => (
          <button
            onClick={onClose}
            className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        ),
        toolbar: () => (
          <div className="flex items-center space-x-2 p-2">
            <button
              onClick={() => setZoom(prev => Math.max(0.5, prev - 0.25))}
              className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
              title="缩小"
            >
              <MagnifyingGlassMinusIcon className="w-5 h-5" />
            </button>

            <span className="text-white text-sm px-2">
              {Math.round(zoom * 100)}%
            </span>

            <button
              onClick={() => setZoom(prev => Math.min(3, prev + 0.25))}
              className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
              title="放大"
            >
              <MagnifyingGlassPlusIcon className="w-5 h-5" />
            </button>

            {onDownload && (
              <button
                onClick={handleDownload}
                className="p-2 rounded-full bg-black bg-opacity-50 text-white hover:bg-opacity-70 transition-all"
                title="下载图片"
              >
                <ArrowDownTrayIcon className="w-5 h-5" />
              </button>
            )}
          </div>
        )
      }}
      carousel={{
        finite: images.length <= 1,
        preload: 2
      }}
      animation={{
        fade: 300,
        swipe: 500
      }}
      controller={{
        closeOnBackdropClick: true,
        closeOnPullDown: true,
        closeOnPullUp: true
      }}
      styles={{
        container: {
          backgroundColor: 'rgba(0, 0, 0, 0.9)'
        }
      }}
    />
  );
}

// 简化的图片预览组件
export function SimpleImagePreview({
  src,
  alt,
  className = '',
  onClick
}: {
  src: string;
  alt?: string;
  className?: string;
  onClick?: () => void;
}) {
  const [loaded, setLoaded] = useState(false);
  const [error, setError] = useState(false);

  return (
    <div
      className={`relative overflow-hidden rounded-lg cursor-pointer group ${className}`}
      onClick={onClick}
    >
      {!loaded && !error && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse flex items-center justify-center">
          <div className="text-gray-400">
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
      )}

      {error ? (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 flex flex-col items-center justify-center">
          <svg className="w-8 h-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span className="text-xs text-gray-500">加载失败</span>
        </div>
      ) : (
        <img
          src={src}
          alt={alt}
          className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
            loaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => setLoaded(true)}
          onError={() => setError(true)}
        />
      )}

      {/* 悬停遮罩 */}
      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <MagnifyingGlassPlusIcon className="w-8 h-8 text-white" />
        </div>
      </div>
    </div>
  );
}
