import type { NextApiRequest, NextApiResponse } from 'next';
import prisma from '@/lib/prisma';
import { isAuthenticated, getCurrentUserId } from '@/lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: '方法不允许' });
  }

  try {
    await isAuthenticated(req, res, async () => {});
  } catch (error) {
    return;
  }

  const userId = await getCurrentUserId(req, res);
  const { notificationId } = req.body;

  if (!notificationId) {
    return res.status(400).json({ message: '缺少通知ID' });
  }

  try {
    // 更新消息为已读状态
    await prisma.chatMessage.update({
      where: {
        id: notificationId,
      },
      data: {
        isRead: true,
      },
    });

    return res.status(200).json({ message: '标记已读成功' });
  } catch (error) {
    console.error('标记已读失败:', error);
    return res.status(500).json({ message: '标记已读失败' });
  }
}
