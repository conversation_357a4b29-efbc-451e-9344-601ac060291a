# LabSync 微信小程序

LabSync实验室管理系统的微信小程序版本，为用户提供便捷的移动端实验室管理体验。

## 📱 功能特性

### 🔐 用户认证
- **账号登录** - 使用邮箱和密码登录
- **微信登录** - 快速微信授权登录
- **账号绑定** - 绑定微信与LabSync账号
- **自动登录** - 记住登录状态，免重复登录

### 📊 项目管理
- **项目列表** - 查看所有参与的项目
- **项目详情** - 查看项目详细信息、进度、成员
- **项目创建** - 创建新项目（需要权限）
- **项目编辑** - 编辑项目信息
- **项目搜索** - 快速搜索项目
- **状态筛选** - 按项目状态筛选

### ✅ 任务管理
- **任务列表** - 查看个人任务和项目任务
- **任务详情** - 查看任务详细信息
- **任务创建** - 创建新任务
- **任务编辑** - 编辑任务信息
- **状态更新** - 更新任务状态
- **优先级管理** - 设置任务优先级

### 💬 聊天系统
- **聊天列表** - 查看所有聊天会话
- **私人聊天** - 与团队成员一对一聊天
- **项目聊天** - 项目团队群聊
- **文件分享** - 在聊天中分享文件
- **消息通知** - 实时消息提醒

### 📁 文件管理
- **文件列表** - 查看项目文件
- **文件上传** - 上传各种格式文件
- **文件预览** - 预览图片、文档等
- **文件下载** - 下载文件到本地

### 🔔 通知系统
- **系统通知** - 接收系统重要通知
- **任务通知** - 任务分配、完成通知
- **项目通知** - 项目状态变更通知
- **消息提醒** - 未读消息提醒

### 👤 个人中心
- **个人资料** - 查看和编辑个人信息
- **头像管理** - 上传和更换头像
- **设置管理** - 个性化设置
- **退出登录** - 安全退出

## 🛠️ 技术架构

### 前端技术
- **微信小程序原生框架** - 使用微信小程序原生开发
- **ES6+** - 现代JavaScript语法
- **模块化设计** - 组件化和服务化架构
- **响应式设计** - 适配不同屏幕尺寸

### 后端集成
- **API复用** - 复用LabSync Web版本的API接口
- **统一数据库** - 与Web版本共享数据库
- **实时同步** - 数据实时同步更新
- **权限一致** - 与Web版本保持一致的权限控制

### 数据管理
- **本地缓存** - 合理使用本地存储提升性能
- **离线支持** - 部分功能支持离线使用
- **数据同步** - 网络恢复时自动同步数据

## 📁 项目结构

```
labsync-miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── config/               # 配置文件
│   └── config.js         # 应用配置
├── services/             # 服务层
│   ├── api.js           # API服务
│   └── auth.js          # 认证服务
├── utils/                # 工具函数
│   └── util.js          # 通用工具
├── pages/                # 页面文件
│   ├── index/           # 首页
│   ├── login/           # 登录页
│   ├── register/        # 注册页
│   ├── projects/        # 项目列表
│   ├── project-detail/  # 项目详情
│   ├── tasks/           # 任务列表
│   ├── task-detail/     # 任务详情
│   ├── chats/           # 聊天列表
│   ├── chat-detail/     # 聊天详情
│   ├── files/           # 文件管理
│   ├── profile/         # 个人中心
│   ├── team/            # 团队管理
│   └── notifications/   # 通知中心
├── components/           # 自定义组件
├── images/              # 图片资源
└── README.md            # 说明文档
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- Node.js 14.0+
- LabSync Web版本后端服务

### 开发步骤

1. **克隆项目**
```bash
git clone https://github.com/BWQ-L/LabSync.git
cd LabSync/labsync-miniprogram
```

2. **配置后端地址**
编辑 `config/config.js` 文件，设置正确的后端API地址：
```javascript
const ENV = {
  development: {
    baseUrl: 'http://localhost:3000', // 修改为你的后端地址
    // ...
  }
}
```

3. **导入微信开发者工具**
- 打开微信开发者工具
- 选择"导入项目"
- 选择 `labsync-miniprogram` 文件夹
- 填写AppID（测试可使用测试号）

4. **启动后端服务**
确保LabSync Web版本的后端服务正在运行：
```bash
cd ../LabSync-main
npm run dev
```

5. **开始开发**
在微信开发者工具中预览和调试小程序

### 配置说明

#### API配置
在 `config/config.js` 中配置后端API地址：
- 开发环境：通常为 `http://localhost:3000`
- 生产环境：替换为实际的服务器域名

#### 小程序配置
在 `app.json` 中配置小程序基本信息：
- 页面路径
- 导航栏样式
- tabBar配置
- 权限设置

## 🔧 开发指南

### 页面开发
每个页面包含四个文件：
- `.js` - 页面逻辑
- `.wxml` - 页面结构
- `.wxss` - 页面样式
- `.json` - 页面配置

### 服务调用
使用统一的API服务：
```javascript
import { apiService } from '../../services/api'

// 获取项目列表
const projects = await apiService.getProjects()
```

### 认证管理
使用认证服务管理登录状态：
```javascript
import { authService } from '../../services/auth'

// 检查登录状态
if (authService.isLoggedIn()) {
  // 已登录逻辑
}
```

### 工具函数
使用通用工具函数：
```javascript
import { formatTime, validateEmail } from '../../utils/util'

// 格式化时间
const timeText = formatTime(date)
```

## 🔗 与Web版本的互通

### 数据同步
- **实时同步** - 小程序与Web版本数据实时同步
- **统一API** - 使用相同的后端API接口
- **权限一致** - 保持与Web版本一致的权限控制

### 功能对应
| Web版本功能 | 小程序功能 | 状态 |
|------------|-----------|------|
| 用户管理 | 个人中心 | ✅ 已实现 |
| 项目管理 | 项目管理 | ✅ 已实现 |
| 任务管理 | 任务管理 | ✅ 已实现 |
| 聊天系统 | 聊天系统 | ✅ 已实现 |
| 文件管理 | 文件管理 | ✅ 已实现 |
| 通知系统 | 通知中心 | ✅ 已实现 |

### 特有功能
- **微信登录** - 小程序特有的微信快速登录
- **扫码功能** - 扫描二维码快速访问项目/任务
- **分享功能** - 微信内分享项目和任务
- **推送通知** - 微信消息推送

## 📱 使用说明

### 首次使用
1. 扫描小程序码或搜索小程序
2. 选择登录方式（账号登录/微信登录）
3. 如果是微信登录，需要先绑定LabSync账号
4. 登录成功后即可使用所有功能

### 日常使用
1. **查看项目** - 在项目页面查看所有参与的项目
2. **管理任务** - 在任务页面查看和管理个人任务
3. **团队沟通** - 在聊天页面与团队成员沟通
4. **文件共享** - 上传和下载项目相关文件
5. **接收通知** - 及时接收项目和任务相关通知

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发规范
- 遵循微信小程序开发规范
- 保持代码风格一致
- 添加必要的注释
- 测试功能完整性

### 提交规范
- 使用清晰的commit message
- 一个commit只做一件事
- 提交前进行充分测试

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情。

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 提交 [Issue](https://github.com/BWQ-L/LabSync/issues)
- 发送邮件到项目维护者

---

**LabSync微信小程序 - 让实验室管理更便捷！** 📱✨
