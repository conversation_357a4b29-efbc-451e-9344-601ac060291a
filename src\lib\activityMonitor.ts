import prisma from './prisma';
import { createSmartNotification, NotificationType, NotificationPriority } from './notifications';

// 活动类型
export enum ActivityType {
  // 用户活动
  USER_LOGIN = 'USER_LOGIN',
  USER_LOGOUT = 'USER_LOGOUT',
  USER_REGISTER = 'USER_REGISTER',
  
  // 项目活动
  PROJECT_CREATED = 'PROJECT_CREATED',
  PROJECT_UPDATED = 'PROJECT_UPDATED',
  PROJECT_DELETED = 'PROJECT_DELETED',
  PROJECT_MEMBER_ADDED = 'PROJECT_MEMBER_ADDED',
  PROJECT_MEMBER_REMOVED = 'PROJECT_MEMBER_REMOVED',
  
  // 任务活动
  TASK_CREATED = 'TASK_CREATED',
  TASK_UPDATED = 'TASK_UPDATED',
  TASK_DELETED = 'TASK_DELETED',
  TASK_ASSIGNED = 'TASK_ASSIGNED',
  TASK_COMPLETED = 'TASK_COMPLETED',
  
  // 文件活动
  FILE_UPLOADED = 'FILE_UPLOADED',
  FILE_DOWNLOADED = 'FILE_DOWNLOADED',
  FILE_DELETED = 'FILE_DELETED',
  
  // 消息活动
  MESSAGE_SENT = 'MESSAGE_SENT',
  CHAT_CREATED = 'CHAT_CREATED',
  
  // 系统活动
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  SYSTEM_WARNING = 'SYSTEM_WARNING',
  SYSTEM_INFO = 'SYSTEM_INFO',
}

// 活动记录接口
export interface ActivityRecord {
  id?: string;
  type: ActivityType;
  userId?: string;
  userName?: string;
  description: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  relatedId?: string;
  relatedType?: 'project' | 'task' | 'file' | 'user' | 'chat';
}

// 活动监控配置
export interface MonitoringConfig {
  enableRealTimeAlerts: boolean;
  alertThresholds: {
    errorRate: number; // 错误率阈值（百分比）
    responseTime: number; // 响应时间阈值（毫秒）
    concurrentUsers: number; // 并发用户数阈值
    failedLogins: number; // 失败登录次数阈值
  };
  retentionDays: number; // 活动记录保留天数
}

// 活动监控服务
export class ActivityMonitorService {
  private static config: MonitoringConfig = {
    enableRealTimeAlerts: true,
    alertThresholds: {
      errorRate: 5, // 5%
      responseTime: 5000, // 5秒
      concurrentUsers: 100,
      failedLogins: 5,
    },
    retentionDays: 90,
  };

  // 记录活动
  static async recordActivity(activity: ActivityRecord): Promise<void> {
    try {
      // 这里可以扩展为将活动记录存储到专门的活动日志表
      // 目前简化处理，可以根据需要创建 Activity 表
      console.log('Activity recorded:', {
        type: activity.type,
        userId: activity.userId,
        description: activity.description,
        timestamp: activity.timestamp,
        metadata: activity.metadata,
      });

      // 检查是否需要发送实时警报
      if (this.config.enableRealTimeAlerts) {
        await this.checkAndSendAlerts(activity);
      }
    } catch (error) {
      console.error('记录活动失败:', error);
    }
  }

  // 记录用户登录
  static async recordUserLogin(userId: string, userName: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.recordActivity({
      type: ActivityType.USER_LOGIN,
      userId,
      userName,
      description: `用户 ${userName} 登录系统`,
      ipAddress,
      userAgent,
      timestamp: new Date(),
    });

    // 更新用户最后登录时间
    try {
      await prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() },
      });
    } catch (error) {
      console.error('更新用户最后登录时间失败:', error);
    }
  }

  // 记录项目创建
  static async recordProjectCreated(projectId: string, projectTitle: string, userId: string, userName: string): Promise<void> {
    await this.recordActivity({
      type: ActivityType.PROJECT_CREATED,
      userId,
      userName,
      description: `${userName} 创建了项目 "${projectTitle}"`,
      timestamp: new Date(),
      relatedId: projectId,
      relatedType: 'project',
      metadata: {
        projectTitle,
      },
    });
  }

  // 记录任务创建
  static async recordTaskCreated(taskId: string, taskTitle: string, projectId: string, userId: string, userName: string): Promise<void> {
    await this.recordActivity({
      type: ActivityType.TASK_CREATED,
      userId,
      userName,
      description: `${userName} 创建了任务 "${taskTitle}"`,
      timestamp: new Date(),
      relatedId: taskId,
      relatedType: 'task',
      metadata: {
        taskTitle,
        projectId,
      },
    });
  }

  // 记录任务完成
  static async recordTaskCompleted(taskId: string, taskTitle: string, projectId: string, userId: string, userName: string): Promise<void> {
    await this.recordActivity({
      type: ActivityType.TASK_COMPLETED,
      userId,
      userName,
      description: `${userName} 完成了任务 "${taskTitle}"`,
      timestamp: new Date(),
      relatedId: taskId,
      relatedType: 'task',
      metadata: {
        taskTitle,
        projectId,
      },
    });
  }

  // 记录文件上传
  static async recordFileUploaded(fileId: string, fileName: string, projectId: string | null, userId: string, userName: string): Promise<void> {
    await this.recordActivity({
      type: ActivityType.FILE_UPLOADED,
      userId,
      userName,
      description: `${userName} 上传了文件 "${fileName}"`,
      timestamp: new Date(),
      relatedId: fileId,
      relatedType: 'file',
      metadata: {
        fileName,
        projectId,
      },
    });
  }

  // 记录系统错误
  static async recordSystemError(error: Error, userId?: string, context?: Record<string, any>): Promise<void> {
    await this.recordActivity({
      type: ActivityType.SYSTEM_ERROR,
      userId,
      description: `系统错误: ${error.message}`,
      timestamp: new Date(),
      metadata: {
        errorName: error.name,
        errorStack: error.stack,
        context,
      },
    });
  }

  // 检查并发送警报
  private static async checkAndSendAlerts(activity: ActivityRecord): Promise<void> {
    try {
      // 检查错误率
      if (activity.type === ActivityType.SYSTEM_ERROR) {
        await this.checkErrorRate();
      }

      // 检查失败登录
      if (activity.type === ActivityType.USER_LOGIN && activity.metadata?.failed) {
        await this.checkFailedLogins(activity.userId!, activity.ipAddress);
      }

      // 检查并发用户数
      if (activity.type === ActivityType.USER_LOGIN) {
        await this.checkConcurrentUsers();
      }
    } catch (error) {
      console.error('检查警报失败:', error);
    }
  }

  // 检查错误率
  private static async checkErrorRate(): Promise<void> {
    // 简化实现，实际应该基于时间窗口计算错误率
    const recentErrors = await this.getRecentActivityCount(ActivityType.SYSTEM_ERROR, 60); // 最近60分钟
    const totalActivities = await this.getRecentActivityCount(null, 60);
    
    if (totalActivities > 0) {
      const errorRate = (recentErrors / totalActivities) * 100;
      
      if (errorRate > this.config.alertThresholds.errorRate) {
        await this.sendSystemAlert(
          '系统错误率过高',
          `系统错误率达到 ${errorRate.toFixed(2)}%，超过阈值 ${this.config.alertThresholds.errorRate}%`,
          NotificationPriority.URGENT
        );
      }
    }
  }

  // 检查失败登录
  private static async checkFailedLogins(userId: string, ipAddress?: string): Promise<void> {
    // 简化实现，检查最近1小时的失败登录次数
    const failedLogins = await this.getRecentFailedLogins(userId, ipAddress, 60);
    
    if (failedLogins > this.config.alertThresholds.failedLogins) {
      await this.sendSecurityAlert(
        '异常登录活动',
        `用户 ${userId} 在最近1小时内失败登录 ${failedLogins} 次`,
        userId
      );
    }
  }

  // 检查并发用户数
  private static async checkConcurrentUsers(): Promise<void> {
    // 简化实现，检查最近活跃的用户数
    const activeUsers = await this.getActiveUserCount(30); // 最近30分钟活跃用户
    
    if (activeUsers > this.config.alertThresholds.concurrentUsers) {
      await this.sendSystemAlert(
        '并发用户数过高',
        `当前活跃用户数 ${activeUsers}，超过阈值 ${this.config.alertThresholds.concurrentUsers}`,
        NotificationPriority.HIGH
      );
    }
  }

  // 发送系统警报
  private static async sendSystemAlert(title: string, message: string, priority: NotificationPriority): Promise<void> {
    // 获取所有管理员
    const admins = await prisma.user.findMany({
      where: { role: 'ADMIN', status: 'APPROVED' },
      select: { id: true },
    });

    // 向所有管理员发送警报
    for (const admin of admins) {
      await createSmartNotification({
        userId: admin.id,
        type: NotificationType.SYSTEM_UPDATE,
        title,
        message,
        priority,
      });
    }
  }

  // 发送安全警报
  private static async sendSecurityAlert(title: string, message: string, userId: string): Promise<void> {
    // 向用户和管理员发送安全警报
    await createSmartNotification({
      userId,
      type: NotificationType.USER_APPROVED, // 临时使用，应该添加安全相关的通知类型
      title,
      message,
      priority: NotificationPriority.URGENT,
    });
  }

  // 获取最近活动数量（简化实现）
  private static async getRecentActivityCount(type: ActivityType | null, minutes: number): Promise<number> {
    // 这里应该查询活动日志表，目前返回模拟数据
    return Math.floor(Math.random() * 100);
  }

  // 获取最近失败登录次数（简化实现）
  private static async getRecentFailedLogins(userId: string, ipAddress?: string, minutes: number): Promise<number> {
    // 这里应该查询活动日志表，目前返回模拟数据
    return Math.floor(Math.random() * 10);
  }

  // 获取活跃用户数（简化实现）
  private static async getActiveUserCount(minutes: number): Promise<number> {
    // 基于最后登录时间计算活跃用户数
    const activeUsers = await prisma.user.count({
      where: {
        lastLoginAt: {
          gte: new Date(Date.now() - minutes * 60 * 1000),
        },
        status: 'APPROVED',
      },
    });

    return activeUsers;
  }

  // 清理过期活动记录
  static async cleanupOldActivities(): Promise<void> {
    try {
      const cutoffDate = new Date(Date.now() - this.config.retentionDays * 24 * 60 * 60 * 1000);
      
      // 这里应该删除活动日志表中的过期记录
      console.log(`清理 ${cutoffDate.toISOString()} 之前的活动记录`);
    } catch (error) {
      console.error('清理过期活动记录失败:', error);
    }
  }

  // 获取系统健康状态
  static async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    metrics: Record<string, any>;
  }> {
    try {
      const activeUsers = await this.getActiveUserCount(30);
      const recentErrors = await this.getRecentActivityCount(ActivityType.SYSTEM_ERROR, 60);
      
      // 简化的健康检查
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (recentErrors > 10) {
        status = 'warning';
      }
      
      if (recentErrors > 50) {
        status = 'critical';
      }

      return {
        status,
        metrics: {
          activeUsers,
          recentErrors,
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
        },
      };
    } catch (error) {
      console.error('获取系统健康状态失败:', error);
      return {
        status: 'critical',
        metrics: {
          error: error instanceof Error ? error.message : '未知错误',
        },
      };
    }
  }
}
