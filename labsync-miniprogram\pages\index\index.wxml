<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else>
    <!-- 用户信息卡片 -->
    <view class="user-card card">
      <view class="user-info">
        <view class="avatar">
          <image src="{{userInfo.avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <view class="user-name">{{userInfo.name}}</view>
          <view class="user-role">{{userInfo.role === 'ADMIN' ? '管理员' : userInfo.role === 'LEADER' ? '项目负责人' : '团队成员'}}</view>
        </view>
      </view>
      <view class="user-actions">
        <button class="btn btn-outline btn-small" bindtap="scanCode">扫码</button>
        <button class="btn btn-primary btn-small" bindtap="goToProfile">个人资料</button>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-box card">
      <input 
        class="search-input" 
        placeholder="搜索项目、任务、文件..." 
        bindinput="onSearchInput"
        confirm-type="search"
      />
    </view>

    <!-- 统计卡片 -->
    <view class="stats-grid">
      <view class="stat-card" bindtap="goToProjects">
        <view class="stat-number">{{stats.projectCount}}</view>
        <view class="stat-label">项目</view>
      </view>
      <view class="stat-card" bindtap="goToTasks">
        <view class="stat-number">{{stats.taskCount}}</view>
        <view class="stat-label">任务</view>
      </view>
      <view class="stat-card" bindtap="goToTasks">
        <view class="stat-number">{{stats.completedTaskCount}}</view>
        <view class="stat-label">已完成</view>
      </view>
      <view class="stat-card" bindtap="goToChats">
        <view class="stat-number">{{stats.unreadMessageCount}}</view>
        <view class="stat-label">未读消息</view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions card">
      <view class="card-header">
        <view class="card-title">快速操作</view>
      </view>
      <view class="action-buttons">
        <button class="action-btn" bindtap="createProject">
          <view class="action-icon">📁</view>
          <view class="action-text">新建项目</view>
        </button>
        <button class="action-btn" bindtap="createTask">
          <view class="action-icon">✅</view>
          <view class="action-text">新建任务</view>
        </button>
        <button class="action-btn" bindtap="goToChats">
          <view class="action-icon">💬</view>
          <view class="action-text">发起聊天</view>
        </button>
        <button class="action-btn" bindtap="goToFiles">
          <view class="action-icon">📎</view>
          <view class="action-text">文件管理</view>
        </button>
      </view>
    </view>

    <!-- 最近项目 -->
    <view class="recent-section card" wx:if="{{recentProjects.length > 0}}">
      <view class="card-header">
        <view class="card-title">最近项目</view>
        <view class="more-link" bindtap="goToProjects">查看全部</view>
      </view>
      <view class="project-list">
        <view 
          class="project-item" 
          wx:for="{{recentProjects}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="goToProjectDetail"
        >
          <view class="project-info">
            <view class="project-title">{{item.title}}</view>
            <view class="project-desc">{{item.description || '暂无描述'}}</view>
            <view class="project-meta">
              <text class="project-time">{{item.updatedAtFormatted}}</text>
              <text class="project-progress">进度: {{item.progress}}%</text>
            </view>
          </view>
          <view class="project-status">
            <view class="status-dot status-{{item.status.toLowerCase()}}"></view>
            <text class="status-text">{{item.status === 'ACTIVE' ? '进行中' : item.status === 'COMPLETED' ? '已完成' : '计划中'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近任务 -->
    <view class="recent-section card" wx:if="{{recentTasks.length > 0}}">
      <view class="card-header">
        <view class="card-title">最近任务</view>
        <view class="more-link" bindtap="goToTasks">查看全部</view>
      </view>
      <view class="task-list">
        <view 
          class="task-item" 
          wx:for="{{recentTasks}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="goToTaskDetail"
        >
          <view class="task-info">
            <view class="task-title">{{item.title}}</view>
            <view class="task-meta">
              <text class="task-project">{{item.project.title}}</text>
              <text class="task-time">{{item.updatedAtFormatted}}</text>
            </view>
          </view>
          <view class="task-status">
            <view class="badge badge-{{item.status === 'COMPLETED' ? 'success' : item.status === 'IN_PROGRESS' ? 'warning' : 'gray'}}">
              {{item.status === 'TODO' ? '待办' : item.status === 'IN_PROGRESS' ? '进行中' : item.status === 'COMPLETED' ? '已完成' : '待审核'}}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近消息 -->
    <view class="recent-section card" wx:if="{{recentMessages.length > 0}}">
      <view class="card-header">
        <view class="card-title">最近消息</view>
        <view class="more-link" bindtap="goToChats">查看全部</view>
      </view>
      <view class="message-list">
        <view 
          class="message-item" 
          wx:for="{{recentMessages}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="goToChatDetail"
        >
          <view class="message-avatar">
            <image src="{{item.otherParticipants[0].avatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.name || item.otherParticipants[0].name}}</view>
            <view class="message-content">{{item.lastMessage.content}}</view>
          </view>
          <view class="message-meta">
            <view class="message-time">{{item.updatedAtFormatted}}</view>
            <view class="message-badge" wx:if="{{item.unreadCount > 0}}">{{item.unreadCount}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty" wx:if="{{!loading && recentProjects.length === 0 && recentTasks.length === 0 && recentMessages.length === 0}}">
      <image class="empty-icon" src="/images/empty.png"></image>
      <view class="empty-text">暂无数据，开始创建您的第一个项目吧！</view>
      <button class="btn btn-primary" bindtap="createProject">创建项目</button>
    </view>
  </view>
</view>
